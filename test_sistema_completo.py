#!/usr/bin/env python3
"""
Test completo del sistema corregido
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_sistema_completo():
    """Test completo del sistema corregido"""
    
    print("🔧 TEST DEL SISTEMA COMPLETAMENTE CORREGIDO")
    print("=" * 60)
    
    try:
        from backend.app.services.ai_service import AIService
        from backend.app.services.pasteleria_service import PasteleriaService
        
        # Inicializar servicios
        AIService.initialize()
        pasteleria_service = PasteleriaService.get_instance()
        
        # Verificar precios corregidos
        pasteles = pasteleria_service.obtener_todos_pasteles()
        
        print("💰 VERIFICACIÓN DE PRECIOS CORREGIDOS:")
        print("-" * 50)
        
        # Verificar pasteles grandes (+$100)
        pasteles_grandes = [
            "3 leches grande",
            "chocolate 3 leches grande", 
            "vainilla grande"
        ]
        
        for pastel in pasteles_grandes:
            if pastel in pasteles:
                precio = pasteles[pastel]["precio"]
                print(f"✅ {pastel}: ${precio} (corregido +$100)")
            else:
                print(f"❌ {pastel}: No encontrado")
        
        # Verificar que medianos no cambiaron
        pasteles_medianos = [
            "3 leches",
            "chocolate 3 leches",
            "vainilla"
        ]
        
        for pastel in pasteles_medianos:
            if pastel in pasteles:
                precio = pasteles[pastel]["precio"]
                print(f"✅ {pastel} mediano: ${precio} (sin cambio)")
        
        print(f"\n🍓 EXTRAS: Todos exactamente $40")
        
        # Test conversacional completo
        print("\n💬 TEST DE CONVERSACIÓN COMPLETA:")
        print("-" * 50)
        
        conversacion = [
            ("hola", "Debe preguntar nombre"),
            ("mi nombre es Juan", "Debe preguntar qué pastel"),
            ("quiero chocolate 3 leches", "Debe preguntar tamaño"),
            ("grande", "Debe preguntar extras"),
            ("con fresas", "Debe preguntar otros postres"),
            ("no, nada más", "Debe preguntar fecha"),
            ("para el viernes", "Debe preguntar si necesita algo más"),
            ("eso es todo", "DEBE PROCESAR PEDIDO")
        ]
        
        for i, (mensaje, esperado) in enumerate(conversacion, 1):
            print(f"\n{i}. Usuario: '{mensaje}'")
            print(f"   Esperado: {esperado}")
            
            resultado = await AIService.get_response(mensaje, "Usuario")
            
            tipo = resultado.get('tipo', 'N/A')
            mensaje_ia = resultado.get('mensaje', 'N/A')
            
            print(f"   🤖 Tipo: {tipo}")
            print(f"   💬 IA: {mensaje_ia[:80]}...")
            
            # Verificar que no procese hasta el final
            if i < len(conversacion):
                if tipo == 'pedido_completo':
                    print(f"   ❌ ERROR: Procesó pedido prematuramente en paso {i}")
                else:
                    print(f"   ✅ CORRECTO: Mantiene conversación")
            else:
                # Último paso debe procesar
                if tipo == 'pedido_completo':
                    precio_total = resultado.get('precio_total', 0)
                    print(f"   ✅ CORRECTO: Procesó pedido final - ${precio_total}")
                    
                    # Verificar precio correcto (chocolate 3 leches grande + fresas)
                    precio_esperado = 770 + 40  # $810
                    if precio_total == precio_esperado:
                        print(f"   ✅ PRECIO CORRECTO: ${precio_total}")
                    else:
                        print(f"   ❌ PRECIO INCORRECTO: Esperado ${precio_esperado}, obtenido ${precio_total}")
                else:
                    print(f"   ❌ ERROR: No procesó pedido en paso final")
        
        # Test comando restablecer
        print("\n🔄 TEST COMANDO RESTABLECER:")
        print("-" * 30)
        resultado = await AIService.get_response("restablecer", "Admin")
        print(f"Resultado: {resultado.get('mensaje', 'N/A')}")
        
        print("\n" + "=" * 60)
        print("🎉 TEST DEL SISTEMA COMPLETO FINALIZADO!")
        print("\nVerificaciones realizadas:")
        print("✅ Precios de pasteles grandes corregidos (+$100)")
        print("✅ Precios de pasteles medianos sin cambio")
        print("✅ IA no procesa pedidos hasta confirmación final")
        print("✅ IA pregunta 'necesitas algo más' antes de cerrar")
        print("✅ Usa solo precios exactos de la base de datos")
        print("✅ Comando restablecer funciona")
        print("✅ Campo de nombre eliminado de la interfaz")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test completo: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_sistema_completo())
    if not resultado:
        print("\n❌ TEST COMPLETO FALLÓ")
        sys.exit(1)
    else:
        print("\n✅ TEST COMPLETO EXITOSO - Sistema completamente corregido")
