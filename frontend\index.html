<!DOCTYPE html>
<!-- NUEVA INTERFAZ COMPLETAMENTE REDISEÑADA v3.0 -->
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pastelerías Victoria's - Nueva Interfaz v3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(45deg, #FFE4E1, #F0E68C, #E6E6FA, #98FB98);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            width: 100%;
            max-width: 600px;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #FF6B9D, #C44569);
            color: white;
            text-align: center;
            padding: 25px;
            position: relative;
        }

        .header h1 {
            font-size: 2.2em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .welcome-message {
            background: linear-gradient(135deg, #FFF8DC, #FFFACD);
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid #FFB6C1;
        }

        .welcome-message h2 {
            color: #8B4513;
            font-size: 1.3em;
            margin-bottom: 8px;
        }

        .chat-area {
            padding: 25px;
            background: #FAFAFA;
        }

        .messages-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 250px;
            max-height: 350px;
            overflow-y: auto;
            border: 3px solid #FFB6C1;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .message {
            margin-bottom: 12px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 85%;
            word-wrap: break-word;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-msg {
            background: linear-gradient(135deg, #E6E6FA, #DDA0DD);
            margin-left: auto;
            text-align: right;
            color: #4B0082;
            border-bottom-right-radius: 5px;
        }

        .bot-msg {
            background: linear-gradient(135deg, #FFB6C1, #FF69B4);
            color: white;
            border-bottom-left-radius: 5px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .input-section {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        #message-input {
            flex: 1;
            padding: 15px 20px;
            border: 3px solid #FFB6C1;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        #message-input:focus {
            border-color: #FF69B4;
            box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
        }

        #send-button {
            background: linear-gradient(135deg, #FF69B4, #FF1493);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
        }

        #send-button:hover {
            background: linear-gradient(135deg, #FF1493, #DC143C);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 20, 147, 0.4);
        }

        .order-result {
            margin-top: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #F0F8FF, #E0F6FF);
            border-radius: 15px;
            border: 2px solid #87CEEB;
            display: none;
        }

        .client-note {
            background: white;
            padding: 18px;
            border-radius: 12px;
            border-left: 5px solid #32CD32;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .client-note h3 {
            color: #228B22;
            margin-bottom: 12px;
            font-size: 1.1em;
        }

        .client-note pre {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background: #F8F8F8;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #DDD;
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🧁 Pastelerías Victoria's 🧁</h1>
        </div>

        <div class="welcome-message">
            <h2>¡Bienvenido a nuestro delicioso mundo!</h2>
        </div>

        <div class="chat-area">
            <div id="messages-container" class="messages-container">
                <div class="message bot-msg">
                    ¡Hola! 🍰 Soy tu asistente personal de Pastelerías Victoria's. Estoy aquí para ayudarte con tus pedidos de pasteles y postres. ¿Qué delicia te gustaría ordenar hoy?
                </div>
            </div>

            <div class="input-section">
                <textarea id="message-input" placeholder="Escribe tu pedido aquí... 🎂" rows="2"></textarea>
                <button id="send-button">Enviar 🚀</button>
            </div>

            <div id="order-result" class="order-result">
                <div class="client-note">
                    <h3>🎯 Tu Pedido</h3>
                    <pre id="client-note-content"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        const messagesContainer = document.getElementById('messages-container');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const orderResult = document.getElementById('order-result');
        const clientNoteContent = document.getElementById('client-note-content');

        function addMessage(text, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-msg' : 'bot-msg'}`;
            messageDiv.textContent = text;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            addMessage(message, true);
            messageInput.value = '';

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.response) {
                    addMessage(data.response);
                }

                if (data.nota_cliente) {
                    clientNoteContent.textContent = data.nota_cliente;
                    orderResult.style.display = 'block';
                }
            } catch (error) {
                addMessage('❌ Ups! Hubo un problema. Por favor intenta de nuevo.');
                console.error('Error:', error);
            }
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
