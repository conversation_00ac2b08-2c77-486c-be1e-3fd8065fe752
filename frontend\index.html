<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pastelería Victoria's - Sistema de Pedidos</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <div class="pasteleria-logo">
                    <i class="fas fa-birthday-cake"></i>
                </div>
            </div>
            <h1>Pastelería Victoria's</h1>
            <p class="subtitle">Sistema de Pedidos Automatizado</p>
        </header>

        <main>
            <!-- Solo mensaje de bienvenida simplificado -->
            <div class="bienvenida-section">
                <div class="bienvenida-content">
                    <h2>🍰 ¡Bienvenido a Pastelería Victoria's! 🍰</h2>
                    <div class="bienvenida-texto">
                        <p>¡Somos una pastelería familiar que desde hace años endulza los momentos más especiales de nuestra comunidad! 💕</p>
                        <p>Nuestros pasteles están hechos con amor, ingredientes frescos y las recetas tradicionales que han pasado de generación en generación. ✨</p>
                        <p>💬 ¡Empecemos! Solo escribe "Hola" y te ayudaré a crear el pedido perfecto para ti.</p>
                    </div>
                </div>
            </div>

            <!-- Chat container -->
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <!-- Los mensajes aparecerán aquí dinámicamente -->
                </div>

                <div class="input-area">
                    <textarea id="user-input" placeholder="Escribe aquí tu mensaje... (ej: Hola, buenos días, qué pasteles tienen)"></textarea>
                    <button id="send-btn" type="button">
                        <i class="fas fa-paper-plane"></i> Enviar
                    </button>
                </div>
            </div>

            <div id="resultado-pedido" class="resultado-pedido" style="display: none;">
                <div class="notas-container">
                    <div class="nota-cliente">
                        <h3><i class="fas fa-receipt"></i> Nota para el Cliente</h3>
                        <pre id="nota-cliente-content"></pre>
                    </div>
                    <!-- Nota de la pastelera oculta - solo para uso interno -->
                    <div class="nota-pastelera" style="display: none;">
                        <h3><i class="fas fa-clipboard-list"></i> Nota para la Pastelera</h3>
                        <pre id="nota-pastelera-content"></pre>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>© 2024 Pastelería Victoria's - Sistema de Pedidos Automatizado</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
