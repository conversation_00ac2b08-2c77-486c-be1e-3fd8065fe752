<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pastelería Victoria's</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #FFB6C1, #E6E6FA, #F0E68C);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #FF69B4, #FFB6C1);
            color: white;
            text-align: center;
            padding: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .welcome {
            background: #FFF8DC;
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid #FFB6C1;
        }

        .welcome h2 {
            color: #8B4513;
            font-size: 1.5em;
        }

        .chat-container {
            padding: 30px;
            min-height: 400px;
        }

        .chat-messages {
            background: #F9F9F9;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 300px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #FFB6C1;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: #E6E6FA;
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: #FFB6C1;
            color: white;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        #user-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #FFB6C1;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
        }

        #user-input:focus {
            border-color: #FF69B4;
        }

        #send-btn {
            background: linear-gradient(45deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }

        #send-btn:hover {
            background: linear-gradient(45deg, #FF1493, #FF69B4);
        }

        .resultado-pedido {
            margin-top: 20px;
            padding: 20px;
            background: #F0F8FF;
            border-radius: 15px;
            border: 2px solid #87CEEB;
        }

        .nota-cliente {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #32CD32;
        }

        .nota-cliente h3 {
            color: #228B22;
            margin-bottom: 15px;
        }

        .nota-cliente pre {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background: #F5F5F5;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #DDD;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍰 Pastelería Victoria's 🍰</h1>
        </div>

        <div class="welcome">
            <h2>¡Bienvenido a Pastelería Victoria's!</h2>
        </div>

        <div class="chat-container">
            <div id="chat-messages" class="chat-messages">
                <div class="message bot-message">
                    ¡Hola! Soy tu asistente de Pastelería Victoria's. ¿En qué puedo ayudarte hoy? Puedes pedirme pasteles, preguntar por nuestro menú o hacer cualquier consulta.
                </div>
            </div>

            <div class="input-area">
                <textarea id="user-input" placeholder="Escribe tu mensaje aquí..." rows="2"></textarea>
                <button id="send-btn">Enviar</button>
            </div>

            <div id="resultado-pedido" class="resultado-pedido" style="display: none;">
                <div class="nota-cliente">
                    <h3>📋 Nota para el Cliente</h3>
                    <pre id="nota-cliente-content"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const resultadoPedido = document.getElementById('resultado-pedido');
        const notaClienteContent = document.getElementById('nota-cliente-content');

        function addMessage(message, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
            messageDiv.textContent = message;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        async function sendMessage() {
            const message = userInput.value.trim();
            if (!message) return;

            addMessage(message, true);
            userInput.value = '';

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                
                if (data.response) {
                    addMessage(data.response);
                }

                if (data.nota_cliente) {
                    notaClienteContent.textContent = data.nota_cliente;
                    resultadoPedido.style.display = 'block';
                }
            } catch (error) {
                addMessage('Error al procesar tu mensaje. Por favor intenta de nuevo.');
                console.error('Error:', error);
            }
        }

        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
