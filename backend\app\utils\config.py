import os
from dotenv import load_dotenv
from pathlib import Path

# Cargar variables de entorno desde el archivo .env
env_path = Path(__file__).resolve().parent.parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

# Configuración de la aplicación
class Settings:
    # Información de la aplicación
    APP_NAME: str = "Pastelería Chatbot - Sistema de Pedidos"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "API para el sistema de pedidos de pastelería con cálculo automático de precios e insumos"

    # Configuración del servidor
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # Configuración de OpenRouter
    OPENROUTER_API_KEY: str = os.getenv("OPENROUTER_API_KEY", "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")
    OPENROUTER_URL: str = "https://openrouter.ai/api/v1/chat/completions"

    # Modelo de IA a utilizar
    AI_MODEL: str = "meta-llama/llama-4-maverick:free"

    # Configuración CORS
    CORS_ORIGINS: list = [
        "http://localhost",
        "http://localhost:8000",
        "http://127.0.0.1",
        "http://127.0.0.1:8000",
    ]

# Crear una instancia de la configuración
settings = Settings()
