#!/usr/bin/env python3
"""
Test simple usando urllib para probar el endpoint
"""

import urllib.request
import urllib.parse
import json

def test_endpoint():
    """Test simple del endpoint de pedidos"""
    
    print("🧪 Probando endpoint de pedidos...")
    
    # Datos del pedido
    data = {
        "pedido": "pastel de chocolate",
        "nombre_cliente": "Test Cliente"
    }
    
    # Convertir a JSON
    json_data = json.dumps(data).encode('utf-8')
    
    # Crear request
    url = "http://localhost:8000/api/pedido"
    req = urllib.request.Request(
        url,
        data=json_data,
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        # Hacer request
        with urllib.request.urlopen(req, timeout=30) as response:
            response_data = response.read().decode('utf-8')
            result = json.loads(response_data)
            
            print(f"✅ Status: {response.status}")
            print(f"📦 Response: {result}")
            
            if result.get("error"):
                print(f"⚠️ Error: {result['error']}")
            else:
                print(f"✅ Éxito: ${result.get('precio_total', 'N/A')}")
                print(f"📝 Mensaje: {result.get('mensaje', 'N/A')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_endpoint()
