#!/usr/bin/env python3
"""
Script para ejecutar el servidor de Pastelería Victoria's
"""

import sys
import os
import traceback

def main():
    try:
        print("🍰 Iniciando Pastelería Victoria's...")
        print("=" * 50)
        
        # Verificar que estamos en el directorio correcto
        if not os.path.exists("backend"):
            print("❌ Error: No se encuentra el directorio 'backend'")
            print("💡 Asegúrate de ejecutar este script desde la raíz del proyecto")
            return
        
        # Importar uvicorn
        try:
            import uvicorn
            print("✅ Uvicorn importado correctamente")
        except ImportError:
            print("❌ Error: uvicorn no está instalado")
            print("💡 Ejecuta: py -m pip install fastapi uvicorn")
            return
        
        # Importar la aplicación
        try:
            from backend.app.main import app
            print("✅ Aplicación importada correctamente")
        except ImportError as e:
            print(f"❌ Error al importar la aplicación: {e}")
            traceback.print_exc()
            return
        
        # Inicializar servicios manualmente
        try:
            from backend.app.services.pasteleria_service import PasteleriaService
            from backend.app.services.ai_service import AIService
            
            PasteleriaService.initialize()
            print("✅ Servicio de pastelería inicializado")
            
            AIService.initialize()
            print("✅ Servicio de AI inicializado")
            
            # Probar que funciona
            servicio = PasteleriaService.get_instance()
            pasteles = servicio.obtener_todos_pasteles()
            print(f"✅ Base de datos cargada: {len(pasteles)} pasteles disponibles")
            
        except Exception as e:
            print(f"❌ Error al inicializar servicios: {e}")
            traceback.print_exc()
            return
        
        print("\n🚀 Iniciando servidor...")
        print("📱 Interfaz web: http://localhost:8000")
        print("📚 Documentación API: http://localhost:8000/docs")
        print("🛑 Presiona Ctrl+C para detener el servidor")
        print("=" * 50)
        
        # Ejecutar el servidor
        uvicorn.run(
            "backend.app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Servidor detenido por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
