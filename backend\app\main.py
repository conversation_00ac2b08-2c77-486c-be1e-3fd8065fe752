"""
Punto de entrada principal para la aplicación FastAPI - Pastelería Chatbot.
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path

from .routers import chat
from .utils.config import settings
from .services.pasteleria_service import PasteleriaService

# Crear la aplicación FastAPI
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir los routers
app.include_router(chat.router)

# Evento de inicio para inicializar los servicios
@app.on_event("startup")
async def startup_event():
    # Inicializar el servicio de pastelería
    PasteleriaService.initialize()

    # Inicializar el servicio AI
    from .services.ai_service import AIService
    AIService.initialize()

# Determinar la ruta al directorio frontend
frontend_dir = Path(__file__).resolve().parent.parent.parent / "frontend"

# Montar los archivos estáticos si el directorio frontend existe
if frontend_dir.exists():
    app.mount("/", StaticFiles(directory=frontend_dir, html=True), name="static")

# La ruta raíz es manejada automáticamente por StaticFiles

@app.get("/health", tags=["health"])
async def health_check():
    """
    Endpoint para verificar el estado de la aplicación.
    """
    return {"status": "ok", "version": settings.APP_VERSION}
