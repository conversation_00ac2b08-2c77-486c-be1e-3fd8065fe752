<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pastelería Victoria's - Sistema de Pedidos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --soft-coral: #FFB3BA;
            --soft-peach: #FFDFBA;
            --soft-yellow: #FFFFBA;
            --soft-mint: #BAFFC9;
            --soft-blue: #BAE1FF;
            --soft-lavender: #E0BBE4;
            --soft-gray: #F5F5F5;
            --warm-white: #FEFEFE;
            --text-dark: #5A5A5A;
            --shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--soft-gray) 0%, var(--soft-blue) 20%, var(--soft-mint) 100%);
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, var(--pastel-pink) 0%, var(--pastel-lavender) 100%);
            color: var(--white);
            border-radius: 15px;
            box-shadow: var(--shadow);
            border-bottom: 4px solid var(--pastel-peach);
        }

        .logo {
            margin-bottom: 15px;
            display: flex;
            justify-content: center;
        }

        .pasteleria-logo {
            position: relative;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--white) 0%, var(--pastel-yellow) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            box-shadow: var(--shadow);
            padding: 5px;
            border: 3px solid var(--pastel-peach);
        }

        .pasteleria-logo i {
            font-size: 2.5rem;
            color: var(--pastel-pink);
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
        }

        .subtitle {
            font-size: 1rem;
            margin-top: 5px;
            color: var(--white);
            font-style: italic;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        main {
            flex: 1;
        }

        .cliente-info {
            background: linear-gradient(135deg, var(--white) 0%, var(--pastel-mint) 20%);
            padding: 20px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
            border-left: 4px solid var(--pastel-blue);
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .input-group label {
            font-weight: 600;
            color: var(--soft-purple);
            font-size: 1rem;
        }

        .input-group input {
            padding: 10px 15px;
            border: 2px solid var(--pastel-blue);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: var(--white);
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--pastel-pink);
            box-shadow: 0 0 0 3px rgba(248, 187, 217, 0.2);
            transform: translateY(-1px);
        }

        .demo-message {
            background: linear-gradient(135deg, var(--pastel-mint) 0%, var(--pastel-yellow) 100%);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid var(--pastel-blue);
            text-align: center;
        }

        .demo-message h2 {
            color: var(--soft-purple);
            margin-bottom: 10px;
        }

        .demo-message p {
            color: var(--soft-purple);
            font-size: 1.1rem;
        }

        footer {
            text-align: center;
            margin-top: 30px;
            padding: 15px;
            color: var(--soft-purple);
            font-size: 0.9rem;
            border-top: 2px solid var(--pastel-mint);
            background: linear-gradient(135deg, var(--white) 0%, var(--pastel-yellow) 20%);
            border-radius: 10px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature {
            background: linear-gradient(135deg, var(--white) 0%, var(--pastel-blue) 10%);
            padding: 20px;
            border-radius: 15px;
            box-shadow: var(--shadow);
            text-align: center;
            border-left: 4px solid var(--pastel-lavender);
        }

        .feature i {
            font-size: 2rem;
            color: var(--pastel-pink);
            margin-bottom: 10px;
        }

        .feature h3 {
            color: var(--soft-purple);
            margin-bottom: 10px;
        }

        .feature p {
            color: var(--soft-purple);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            header {
                padding: 15px;
            }

            h1 {
                font-size: 1.5rem;
            }

            .pasteleria-logo {
                width: 60px;
                height: 60px;
            }

            .pasteleria-logo i {
                font-size: 2rem;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <div class="pasteleria-logo">
                    <i class="fas fa-birthday-cake"></i>
                </div>
            </div>
            <h1>Pastelería Victoria's</h1>
            <p class="subtitle">Sistema de Pedidos Automatizado</p>
        </header>

        <main>
            <div class="cliente-info">
                <div class="input-group">
                    <label for="nombre-cliente">Nombre del cliente:</label>
                    <input type="text" id="nombre-cliente" placeholder="Ingresa tu nombre" value="Cliente">
                </div>
            </div>

            <div class="demo-message">
                <h2>🎂 ¡Bienvenido a Pastelería Victoria's! 🎂</h2>
                <p>Esta es una demostración de la interfaz con los nuevos colores suaves y amenos.</p>
                <p>El sistema completo usa OpenRouter AI para procesar pedidos automáticamente.</p>
                <p><strong>Características:</strong></p>
                <ul style="text-align: left; margin: 10px 0; padding-left: 20px;">
                    <li>🤖 Inteligencia Artificial con OpenRouter</li>
                    <li>🍰 Menú completo visible</li>
                    <li>💰 Extras todos a $40</li>
                    <li>🔍 Detección inteligente de palabras clave</li>
                    <li>📄 Notas duales automáticas</li>
                </ul>
            </div>

            <div class="features">
                <div class="feature">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>Pedidos Inteligentes</h3>
                    <p>Procesamiento automático de pedidos con reconocimiento de pasteles y extras</p>
                </div>
                <div class="feature">
                    <i class="fas fa-calculator"></i>
                    <h3>Cálculo Automático</h3>
                    <p>Precio total y cálculo de insumos necesarios para cada pedido</p>
                </div>
                <div class="feature">
                    <i class="fas fa-receipt"></i>
                    <h3>Notas Duales</h3>
                    <p>Genera notas separadas para cliente y pastelera con información específica</p>
                </div>
            </div>
        </main>

        <footer>
            <p>© 2024 Pastelería Victoria's - Sistema de Pedidos Automatizado</p>
        </footer>
    </div>
</body>
</html>
