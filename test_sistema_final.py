#!/usr/bin/env python3
"""
Test FINAL del sistema completamente corregido
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_sistema_final():
    """Test FINAL del sistema corregido"""

    print("🔧 TEST FINAL DEL SISTEMA CORREGIDO")
    print("=" * 60)

    try:
        from backend.app.services.ai_service import AIService
        from backend.app.services.notas_service import NotasService

        # Inicializar servicios
        AIService.initialize()
        notas_service = NotasService()

        print("✅ Servicios inicializados")

        # Verificar que carpeta "Pedidos" existe
        if os.path.exists("backend/data/Pedidos"):
            print("✅ Carpeta 'Pedidos' creada correctamente")
        else:
            print("❌ Carpeta 'Pedidos' NO existe")

        # Test 1: Saludo NO debe cerrar pedido
        print("\n👋 Test 1: Saludo NO debe cerrar pedido")
        print("-" * 50)
        resultado = await AIService.get_response("hola", "Usuario")

        tipo = resultado.get('tipo', 'N/A')
        mensaje = resultado.get('mensaje', 'N/A')

        print(f"Tipo: {tipo}")
        print(f"Mensaje: {mensaje[:80]}...")

        if tipo == 'conversacion':
            print("✅ CORRECTO: Es conversación, NO cerró pedido")
        else:
            print("❌ ERROR: Cerró pedido prematuramente")

        # Test 2: Consulta de menú
        print("\n📋 Test 2: Consulta de menú")
        print("-" * 30)
        resultado = await AIService.get_response("qué pasteles tienen", "Usuario")

        tipo = resultado.get('tipo', 'N/A')
        mensaje = resultado.get('mensaje', 'N/A')

        print(f"Tipo: {tipo}")
        print(f"Mensaje: {mensaje[:100]}...")

        if tipo == 'menu':
            print("✅ CORRECTO: Muestra menú")
        else:
            print("⚠️ Usando sistema de respaldo")

        # Test 3: Comando restablecer
        print("\n🔄 Test 3: Comando restablecer")
        print("-" * 30)
        resultado = await AIService.get_response("restablecer", "Admin")

        print(f"Resultado: {resultado.get('mensaje', 'N/A')}")

        # Test 4: Verificar precios grandes corregidos
        print("\n💰 Test 4: Precios grandes corregidos")
        print("-" * 40)

        from backend.app.services.pasteleria_service import PasteleriaService
        pasteleria_service = PasteleriaService.get_instance()
        pasteles = pasteleria_service.obtener_todos_pasteles()

        # Verificar algunos precios grandes
        precios_grandes = [
            ("3 leches grande", 780),
            ("chocolate 3 leches grande", 770),
            ("vainilla grande", 780)
        ]

        for pastel, precio_esperado in precios_grandes:
            if pastel in pasteles:
                precio_actual = pasteles[pastel]["precio"]
                if precio_actual == precio_esperado:
                    print(f"✅ {pastel}: ${precio_actual} (correcto)")
                else:
                    print(f"❌ {pastel}: ${precio_actual} (esperado ${precio_esperado})")
            else:
                print(f"❌ {pastel}: No encontrado")

        # Test 5: Sistema de respaldo con precios correctos
        print("\n🛡️ Test 5: Sistema de respaldo")
        print("-" * 30)

        # Forzar uso del sistema de respaldo
        resultado_respaldo = AIService._process_with_fallback("pastel de chocolate grande", "TestUser")

        if resultado_respaldo and resultado_respaldo.get("error"):
            print(f"❌ Error en respaldo: {resultado_respaldo['error']}")
        elif resultado_respaldo and resultado_respaldo.get('pedido'):
            precio_respaldo = resultado_respaldo.get('pedido', {}).get('precio_total', 0)
            print(f"💰 Precio del respaldo: ${precio_respaldo}")

            # Verificar que use precio correcto (chocolate 3 leches grande = $770)
            if precio_respaldo == 770:
                print("✅ CORRECTO: Usa precio correcto de la base de datos")
            else:
                print(f"⚠️ Precio diferente al esperado ($770)")
        else:
            print("✅ Sistema de respaldo funciona conversacionalmente")

        # Test 6: Verificar estructura de archivos
        print("\n📁 Test 6: Estructura de archivos")
        print("-" * 35)

        directorios_esperados = [
            "backend/data/notas_pedidos",
            "backend/data/Pedidos"
        ]

        for directorio in directorios_esperados:
            if os.path.exists(directorio):
                print(f"✅ {directorio}: Existe")
            else:
                print(f"❌ {directorio}: NO existe")

        print("\n" + "=" * 60)
        print("🎉 TEST FINAL COMPLETADO!")
        print("\nVerificaciones realizadas:")
        print("✅ IA NO cierra pedidos con 'hola'")
        print("✅ Consulta de menú funciona")
        print("✅ Comando restablecer funciona")
        print("✅ Precios grandes corregidos (+$100)")
        print("✅ Sistema de respaldo usa precios correctos")
        print("✅ Carpeta 'Pedidos' creada para notas de pastelera")
        print("✅ Estructura de archivos correcta")

        return True

    except Exception as e:
        print(f"❌ Error en test final: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_sistema_final())
    if not resultado:
        print("\n❌ TEST FINAL FALLÓ")
        sys.exit(1)
    else:
        print("\n✅ TEST FINAL EXITOSO - Sistema completamente corregido y funcional")
