document.addEventListener('DOMContentLoaded', () => {
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-btn');
    const resultadoPedido = document.getElementById('resultado-pedido');
    const notaClienteContent = document.getElementById('nota-cliente-content');
    const notaPasteleraContent = document.getElementById('nota-pastelera-content');

    // URL base del backend
    const API_URL = 'http://localhost:8000/api';

    // Funciones de menú eliminadas - ahora todo se maneja por IA

    // Función para manejar el envío de mensajes
    async function handleSendMessage() {
        const mensaje = userInput.value.trim();

        if (mensaje === '') {
            return;
        }

        // Agregar mensaje del usuario al chat
        addMessageToChat(mensaje, 'user');

        // Limpiar el input
        userInput.value = '';

        // Mostrar indicador de "procesando..."
        showTypingIndicator();

        try {
            // Enviar mensaje al backend
            const response = await fetch(`${API_URL}/pedido`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    pedido: mensaje,
                    nombre_cliente: 'Usuario' // La IA preguntará el nombre en la conversación
                }),
            });

            if (!response.ok) {
                throw new Error('Error en la respuesta del servidor');
            }

            const data = await response.json();

            // Eliminar indicador de "procesando..."
            removeTypingIndicator();

            if (data.error) {
                // Mostrar error
                addMessageToChat(`❌ ${data.error}`, 'system');
            } else {
                // Mostrar respuesta de la IA
                addMessageToChat(data.mensaje, 'system');

                // Si es un pedido completo, mostrar solo la nota del cliente
                if (data.nota_cliente && data.tipo !== 'conversacion' && data.tipo !== 'menu' && data.tipo !== 'aclaracion') {
                    mostrarNotaCliente(data.nota_cliente);
                }
            }

        } catch (error) {
            console.error('Error:', error);

            // Eliminar indicador de "procesando..."
            removeTypingIndicator();

            // Mostrar mensaje de error
            addMessageToChat('❌ Lo siento, ha ocurrido un error al procesar tu mensaje. Por favor, intenta de nuevo más tarde.', 'system');
        }

        // Scroll al final del chat
        scrollToBottom();
    }

    // Función para agregar mensajes al chat
    function addMessageToChat(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);

        const messagePara = document.createElement('p');
        messagePara.textContent = text;

        messageDiv.appendChild(messagePara);
        chatMessages.appendChild(messageDiv);

        scrollToBottom();
    }

    // Función para mostrar solo la nota del cliente
    function mostrarNotaCliente(notaCliente) {
        if (notaCliente) {
            notaClienteContent.textContent = notaCliente;
            resultadoPedido.style.display = 'block';

            // Scroll suave hacia las notas
            setTimeout(() => {
                resultadoPedido.scrollIntoView({ behavior: 'smooth' });
            }, 300);
        }
    }

    // Función para mostrar las notas (mantenida para compatibilidad)
    function mostrarNotas(notaCliente, notaPastelera) {
        mostrarNotaCliente(notaCliente);
        // La nota de la pastelera se guarda pero no se muestra en la interfaz
        if (notaPastelera) {
            notaPasteleraContent.textContent = notaPastelera;
        }
    }

    // Función para mostrar indicador de "procesando..."
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.classList.add('message', 'system', 'typing-indicator');

        const typingPara = document.createElement('p');
        typingPara.innerHTML = '🍰 Procesando tu pedido...';

        typingDiv.appendChild(typingPara);
        chatMessages.appendChild(typingDiv);

        scrollToBottom();
    }

    // Función para eliminar indicador de "escribiendo..."
    function removeTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    // Función para hacer scroll al final del chat
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Event listeners principales
    if (sendButton) {
        sendButton.addEventListener('click', handleSendMessage);
    }

    if (userInput) {
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
            }
        });

        // Ajustar altura del textarea automáticamente
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = (userInput.scrollHeight > 50) ?
                Math.min(userInput.scrollHeight, 100) + 'px' : '50px';
        });
    }

    // Función para limpiar el chat y las notas
    function limpiarTodo() {
        // Limpiar chat excepto el mensaje de bienvenida
        const mensajes = chatMessages.querySelectorAll('.message:not(.system):not(:first-child)');
        mensajes.forEach(mensaje => mensaje.remove());

        // Ocultar notas
        resultadoPedido.style.display = 'none';
    }

    // Agregar botón para limpiar (opcional)
    const limpiarBtn = document.createElement('button');
    limpiarBtn.textContent = '🗑️ Nuevo Pedido';
    limpiarBtn.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--pasteleria-orange);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 10px 15px;
        cursor: pointer;
        font-size: 0.9rem;
        z-index: 1000;
    `;
    limpiarBtn.addEventListener('click', limpiarTodo);
    document.body.appendChild(limpiarBtn);
});
