document.addEventListener('DOMContentLoaded', () => {
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-btn');
    const resultadoPedido = document.getElementById('resultado-pedido');
    const notaClienteContent = document.getElementById('nota-cliente-content');
    const notaPasteleraContent = document.getElementById('nota-pastelera-content');

    // Elementos de la nueva interfaz
    const btnVerMenu = document.getElementById('btn-ver-menu');
    const btnCerrarMenu = document.getElementById('btn-cerrar-menu');
    const menuSection = document.getElementById('menu-section');
    const menuContent = document.getElementById('menu-content');

    // URL base del backend
    const API_URL = 'http://localhost:8000/api';

    // Event listeners para los botones
    if (btnVerMenu) {
        btnVerMenu.addEventListener('click', mostrarMenu);
    }
    if (btnCerrarMenu) {
        btnCerrarMenu.addEventListener('click', cerrarMenu);
    }

    // Función para mostrar el menú
    async function mostrarMenu() {
        try {
            const response = await fetch(`${API_URL}/menu-descripciones`);
            let menuData;

            if (response.ok) {
                menuData = await response.json();
            } else {
                // Si no se puede conectar al backend, usar menú estático
                menuData = obtenerMenuEstatico();
            }

            renderizarMenu(menuData);
            menuSection.style.display = 'block';
            menuSection.scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            console.log('No se pudo conectar al backend, mostrando menú estático');
            const menuData = obtenerMenuEstatico();
            renderizarMenu(menuData);
            menuSection.style.display = 'block';
            menuSection.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // Función para cerrar el menú
    function cerrarMenu() {
        menuSection.style.display = 'none';
    }

    // Función para renderizar el menú con descripciones
    function renderizarMenu(menuData) {
        menuContent.innerHTML = '';

        // Renderizar cada categoría
        for (const [categoria, datos] of Object.entries(menuData)) {
            const categoriaDiv = document.createElement('div');
            categoriaDiv.className = 'menu-categoria';

            // Título y descripción
            categoriaDiv.innerHTML = `
                <h3>${getIconoCategoria(categoria)} ${datos.titulo}</h3>
                <p class="descripcion">${datos.descripcion}</p>
            `;

            // Contenedor de items
            const itemsContainer = document.createElement('div');

            if (categoria === 'extras') {
                itemsContainer.className = 'extras-grid';
                datos.items.forEach(item => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'extra-item';
                    itemDiv.onclick = () => agregarAlPedido(item.nombre);

                    itemDiv.innerHTML = `
                        <div class="extra-item-info">
                            <div class="extra-item-name">${item.nombre}</div>
                            <div class="extra-item-descripcion">${item.descripcion}</div>
                        </div>
                        <div class="extra-item-precio">$40</div>
                    `;

                    itemsContainer.appendChild(itemDiv);
                });
            } else {
                itemsContainer.className = 'menu-items-grid';
                datos.items.forEach(item => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'menu-item';
                    itemDiv.onclick = () => agregarAlPedido(item.nombre);

                    itemDiv.innerHTML = `
                        <div class="menu-item-header">
                            <div class="menu-item-name">${item.nombre}</div>
                            <div class="menu-item-rebanadas">${item.rebanadas} rebanada${item.rebanadas > 1 ? 's' : ''}</div>
                        </div>
                        <div class="menu-item-descripcion">${item.descripcion}</div>
                    `;

                    itemsContainer.appendChild(itemDiv);
                });
            }

            categoriaDiv.appendChild(itemsContainer);
            menuContent.appendChild(categoriaDiv);
        }
    }

    // Función para obtener icono según categoría
    function getIconoCategoria(categoria) {
        const iconos = {
            'pasteles_grandes': '<i class="fas fa-birthday-cake"></i>',
            'pasteles_medianos': '<i class="fas fa-cake"></i>',
            'pasteles_chicos': '<i class="fas fa-cupcake"></i>',
            'pasteles_mini': '<i class="fas fa-cookie-bite"></i>',
            'otros_postres': '<i class="fas fa-cookie"></i>',
            'extras': '<i class="fas fa-plus-circle"></i>'
        };
        return iconos[categoria] || '<i class="fas fa-cake"></i>';
    }

    // Función para obtener menú estático cuando no hay conexión
    function obtenerMenuEstatico() {
        return {
            "pasteles_grandes": {
                "titulo": "Pasteles Grandes (15 personas) - Perfectos para grandes celebraciones",
                "descripcion": "Nuestros pasteles más grandes, ideales para fiestas y eventos especiales:",
                "items": [
                    {"nombre": "3 Leches Grande", "descripcion": "Clásico pastel empapado en tres tipos de leche, suave y cremoso", "rebanadas": 15},
                    {"nombre": "Vainilla Grande", "descripcion": "Tradicional pastel de vainilla, esponjoso y aromático", "rebanadas": 15},
                    {"nombre": "Chocolate 3 Leches Grande", "descripcion": "La perfecta combinación de chocolate y tres leches", "rebanadas": 15},
                    {"nombre": "Zanahoria Grande", "descripcion": "Pastel húmedo de zanahoria con especias y nueces", "rebanadas": 15}
                ]
            },
            "pasteles_medianos": {
                "titulo": "Pasteles Medianos (12 rebanadas) - Perfectos para compartir",
                "descripcion": "Tenemos unos pasteles muy buenos, esponjosos y deliciosos:",
                "items": [
                    {"nombre": "3 Leches", "descripcion": "Clásico pastel empapado en tres tipos de leche, suave y cremoso", "rebanadas": 12},
                    {"nombre": "Vainilla", "descripcion": "Tradicional pastel de vainilla, esponjoso y aromático", "rebanadas": 12},
                    {"nombre": "Chocolate 3 Leches", "descripcion": "La perfecta combinación de chocolate y tres leches", "rebanadas": 12},
                    {"nombre": "Zanahoria", "descripcion": "Pastel húmedo de zanahoria con especias y nueces", "rebanadas": 12}
                ]
            },
            "pasteles_chicos": {
                "titulo": "Pasteles Chicos (6 rebanadas) - Perfectos para ocasiones íntimas",
                "descripcion": "Nuestros pasteles en tamaño chico para reuniones pequeñas:",
                "items": [
                    {"nombre": "3 Leches Chico", "descripcion": "Clásico 3 leches en tamaño chico", "rebanadas": 6},
                    {"nombre": "Vainilla Chico", "descripcion": "Tradicional vainilla en tamaño chico", "rebanadas": 6},
                    {"nombre": "Chocolate 3 Leches Chico", "descripcion": "Chocolate y tres leches en tamaño chico", "rebanadas": 6},
                    {"nombre": "Zanahoria Chico", "descripcion": "Zanahoria con especias en tamaño chico", "rebanadas": 6}
                ]
            },
            "pasteles_mini": {
                "titulo": "Pasteles Mini (1 rebanada) - Perfectos para una persona",
                "descripcion": "Todos nuestros sabores en porciones individuales:",
                "items": [
                    {"nombre": "3 Leches Mini", "descripcion": "Porción individual de nuestro clásico 3 leches", "rebanadas": 1},
                    {"nombre": "Vainilla Mini", "descripcion": "Porción individual de vainilla", "rebanadas": 1},
                    {"nombre": "Chocolate 3 Leches Mini", "descripcion": "Porción individual de chocolate 3 leches", "rebanadas": 1},
                    {"nombre": "Zanahoria Mini", "descripcion": "Porción individual de zanahoria", "rebanadas": 1}
                ]
            },
            "otros_postres": {
                "titulo": "Otros Postres Deliciosos",
                "descripcion": "Variedad de postres especiales para todos los gustos:",
                "items": [
                    {"nombre": "Cakepop", "descripcion": "Pequeñas bolitas de pastel cubiertas de chocolate", "rebanadas": 1},
                    {"nombre": "Carlota", "descripcion": "Postre frío con galletas y crema", "rebanadas": 1},
                    {"nombre": "Tiramisu", "descripcion": "Clásico postre italiano con café y mascarpone", "rebanadas": 1}
                ]
            },
            "extras": {
                "titulo": "Extras Deliciosos ($40 cada uno)",
                "descripcion": "Personaliza tu pastel con nuestros extras:",
                "items": [
                    {"nombre": "Fresas", "descripcion": "Fresas frescas y jugosas"},
                    {"nombre": "Chocolate", "descripcion": "Chocolate extra para los amantes del cacao"},
                    {"nombre": "Nueces", "descripcion": "Nueces crujientes y nutritivas"},
                    {"nombre": "Caramelo", "descripcion": "Delicioso caramelo casero"},
                    {"nombre": "Crema", "descripcion": "Crema extra suave y cremosa"}
                ]
            }
        };
    }



    // Función para agregar item al pedido al hacer clic
    function agregarAlPedido(item) {
        const currentText = userInput.value.trim();
        const newText = currentText ? `${currentText}, ${item}` : item;
        userInput.value = newText;
        userInput.focus();
    }

    // Función para manejar el envío de mensajes
    async function handleSendMessage() {
        const mensaje = userInput.value.trim();

        if (mensaje === '') {
            return;
        }

        // Agregar mensaje del usuario al chat
        addMessageToChat(mensaje, 'user');

        // Limpiar el input
        userInput.value = '';

        // Mostrar indicador de "procesando..."
        showTypingIndicator();

        try {
            // Enviar mensaje al backend
            const response = await fetch(`${API_URL}/pedido`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    pedido: mensaje,
                    nombre_cliente: 'Cliente' // La IA preguntará el nombre
                }),
            });

            if (!response.ok) {
                throw new Error('Error en la respuesta del servidor');
            }

            const data = await response.json();

            // Eliminar indicador de "procesando..."
            removeTypingIndicator();

            if (data.error) {
                // Mostrar error
                addMessageToChat(`❌ ${data.error}`, 'system');
            } else {
                // Mostrar respuesta de la IA
                addMessageToChat(data.mensaje, 'system');

                // Si es un pedido completo, mostrar solo la nota del cliente
                if (data.nota_cliente && data.tipo !== 'conversacion' && data.tipo !== 'menu' && data.tipo !== 'aclaracion') {
                    mostrarNotaCliente(data.nota_cliente);
                }
            }

        } catch (error) {
            console.error('Error:', error);

            // Eliminar indicador de "procesando..."
            removeTypingIndicator();

            // Mostrar mensaje de error
            addMessageToChat('❌ Lo siento, ha ocurrido un error al procesar tu mensaje. Por favor, intenta de nuevo más tarde.', 'system');
        }

        // Scroll al final del chat
        scrollToBottom();
    }

    // Función para agregar mensajes al chat
    function addMessageToChat(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);

        const messagePara = document.createElement('p');
        messagePara.textContent = text;

        messageDiv.appendChild(messagePara);
        chatMessages.appendChild(messageDiv);

        scrollToBottom();
    }

    // Función para mostrar solo la nota del cliente
    function mostrarNotaCliente(notaCliente) {
        if (notaCliente) {
            notaClienteContent.textContent = notaCliente;
            resultadoPedido.style.display = 'block';

            // Scroll suave hacia las notas
            setTimeout(() => {
                resultadoPedido.scrollIntoView({ behavior: 'smooth' });
            }, 300);
        }
    }

    // Función para mostrar las notas (mantenida para compatibilidad)
    function mostrarNotas(notaCliente, notaPastelera) {
        mostrarNotaCliente(notaCliente);
        // La nota de la pastelera se guarda pero no se muestra en la interfaz
        if (notaPastelera) {
            notaPasteleraContent.textContent = notaPastelera;
        }
    }

    // Función para mostrar indicador de "procesando..."
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.classList.add('message', 'system', 'typing-indicator');

        const typingPara = document.createElement('p');
        typingPara.innerHTML = '🍰 Procesando tu pedido...';

        typingDiv.appendChild(typingPara);
        chatMessages.appendChild(typingDiv);

        scrollToBottom();
    }

    // Función para eliminar indicador de "escribiendo..."
    function removeTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    // Función para hacer scroll al final del chat
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Event listeners principales
    if (sendButton) {
        sendButton.addEventListener('click', handleSendMessage);
    }

    if (userInput) {
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
            }
        });

        // Ajustar altura del textarea automáticamente
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = (userInput.scrollHeight > 50) ?
                Math.min(userInput.scrollHeight, 100) + 'px' : '50px';
        });
    }

    // Función para limpiar el chat y las notas
    function limpiarTodo() {
        // Limpiar chat excepto el mensaje de bienvenida
        const mensajes = chatMessages.querySelectorAll('.message:not(.system):not(:first-child)');
        mensajes.forEach(mensaje => mensaje.remove());

        // Ocultar notas
        resultadoPedido.style.display = 'none';
    }

    // Agregar botón para limpiar (opcional)
    const limpiarBtn = document.createElement('button');
    limpiarBtn.textContent = '🗑️ Nuevo Pedido';
    limpiarBtn.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--pasteleria-orange);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 10px 15px;
        cursor: pointer;
        font-size: 0.9rem;
        z-index: 1000;
    `;
    limpiarBtn.addEventListener('click', limpiarTodo);
    document.body.appendChild(limpiarBtn);
});
