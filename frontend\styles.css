:root {
    --soft-coral: #FFB3BA; /* Coral suave */
    --soft-peach: #FFDFBA; /* Durazno suave */
    --soft-yellow: #FFFFBA; /* Amarillo suave */
    --soft-mint: #BAFFC9; /* Menta suave */
    --soft-blue: #BAE1FF; /* Azul suave */
    --soft-lavender: #E0BBE4; /* Lavanda suave */
    --soft-gray: #F5F5F5; /* Gris suave */
    --warm-white: #FEFEFE; /* Blanco cálido */
    --text-dark: #5A5A5A; /* Texto oscuro suave */
    --shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, var(--soft-gray) 0%, var(--soft-blue) 20%, var(--soft-mint) 100%);
    color: var(--text-dark);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, var(--soft-coral) 0%, var(--soft-peach) 100%);
    color: var(--warm-white);
    border-radius: 20px;
    box-shadow: var(--shadow);
    border-bottom: 4px solid var(--soft-yellow);
}

.logo {
    margin-bottom: 15px;
    display: flex;
    justify-content: center;
}

.pasteleria-logo {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--white) 0%, var(--pastel-yellow) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: var(--shadow);
    padding: 5px;
    border: 3px solid var(--pastel-peach);
}

.pasteleria-logo i {
    font-size: 2.5rem;
    color: var(--pastel-pink);
    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
}

.subtitle {
    font-size: 1rem;
    margin-top: 5px;
    color: var(--white);
    font-style: italic;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
}

h1 {
    font-size: 2rem;
    margin-bottom: 5px;
}

main {
    flex: 1;
}

.cliente-info {
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--soft-mint) 20%);
    padding: 20px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    border-left: 4px solid var(--soft-blue);
}

/* Estilos para la bienvenida */
.bienvenida-section {
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--soft-yellow) 10%);
    padding: 30px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 25px;
    border: 2px solid var(--soft-peach);
    text-align: center;
}

.bienvenida-content h2 {
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.ubicacion {
    background: linear-gradient(135deg, var(--soft-mint) 0%, var(--soft-blue) 100%);
    padding: 25px;
    border-radius: 15px;
    margin: 20px 0;
    border-left: 4px solid var(--soft-coral);
    text-align: left;
}

.ubicacion p {
    margin: 8px 0;
    color: var(--text-dark);
    font-size: 1rem;
}

.ubicacion strong {
    color: var(--text-dark);
}

.bienvenida-texto {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--soft-yellow) 20%);
    border-radius: 12px;
    border-left: 4px solid var(--soft-lavender);
}

.bienvenida-texto p {
    margin: 12px 0;
    color: var(--text-dark);
    font-size: 1rem;
    line-height: 1.6;
    text-align: justify;
}

.bienvenida-texto p:first-child {
    font-weight: 600;
    color: var(--soft-coral);
}

.opciones-menu {
    margin-top: 25px;
}

.opciones-menu p {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 500;
}

.botones-menu {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-opcion {
    background: linear-gradient(135deg, var(--soft-coral) 0%, var(--soft-lavender) 100%);
    color: var(--warm-white);
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-opcion:hover {
    background: linear-gradient(135deg, var(--soft-lavender) 0%, var(--soft-blue) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Estilos para el menú */
.menu-section {
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--soft-yellow) 10%);
    padding: 25px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 25px;
    border: 2px solid var(--soft-peach);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.menu-header h2 {
    color: var(--text-dark);
    font-size: 1.8rem;
    margin: 0;
}

.btn-cerrar {
    background: var(--soft-coral);
    color: var(--warm-white);
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-cerrar:hover {
    background: var(--soft-lavender);
    transform: scale(1.05);
}

.menu-section h2 {
    text-align: center;
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.8rem;
    font-weight: 600;
}

.menu-content {
    display: grid;
    gap: 25px;
}

.menu-categoria {
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--soft-lavender) 15%);
    padding: 25px;
    border-radius: 15px;
    box-shadow: var(--shadow);
    border-left: 4px solid var(--soft-coral);
}

.menu-categoria h3 {
    color: var(--text-dark);
    margin-bottom: 10px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.menu-categoria .descripcion {
    color: var(--text-dark);
    font-style: italic;
    margin-bottom: 20px;
    font-size: 1rem;
}

.menu-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.menu-item {
    background: var(--warm-white);
    padding: 15px;
    border-radius: 12px;
    border-left: 3px solid var(--soft-mint);
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.menu-item:hover {
    background: var(--soft-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.menu-item-name {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.menu-item-rebanadas {
    background: var(--soft-yellow);
    color: var(--text-dark);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
}

.menu-item-descripcion {
    color: var(--text-dark);
    font-size: 0.9rem;
    line-height: 1.4;
    opacity: 0.8;
}

/* Estilos para extras */
.extras-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.extra-item {
    background: var(--warm-white);
    padding: 12px 15px;
    border-radius: 10px;
    border-left: 3px solid var(--soft-coral);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.extra-item:hover {
    background: var(--soft-peach);
    transform: translateX(3px);
}

.extra-item-info {
    flex: 1;
}

.extra-item-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 2px;
}

.extra-item-descripcion {
    font-size: 0.8rem;
    color: var(--text-dark);
    opacity: 0.7;
}

.extra-item-precio {
    background: var(--soft-coral);
    color: var(--warm-white);
    padding: 6px 10px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1rem;
}

.input-group input {
    padding: 10px 15px;
    border: 2px solid var(--soft-blue);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--warm-white);
}

.input-group input:focus {
    outline: none;
    border-color: var(--soft-coral);
    box-shadow: 0 0 0 3px rgba(255, 179, 186, 0.2);
    transform: translateY(-1px);
}

.chat-container {
    background: linear-gradient(135deg, var(--warm-white) 0%, var(--soft-blue) 5%);
    border-radius: 20px;
    box-shadow: var(--shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 45vh;
    border: 2px solid var(--soft-lavender);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.message {
    margin-bottom: 15px;
    padding: 12px 15px;
    border-radius: 10px;
    max-width: 80%;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message p {
    margin: 0;
}

.system {
    background: linear-gradient(135deg, var(--soft-mint) 0%, var(--soft-yellow) 100%);
    border-left: 4px solid var(--soft-blue);
    align-self: flex-start;
    margin-right: auto;
    color: var(--text-dark);
}

.user {
    background: linear-gradient(135deg, var(--soft-coral) 0%, var(--soft-lavender) 100%);
    color: var(--warm-white);
    align-self: flex-end;
    margin-left: auto;
    border-right: 4px solid var(--soft-peach);
}

.input-area {
    display: flex;
    padding: 15px;
    background: linear-gradient(135deg, var(--soft-gray) 0%, var(--soft-mint) 20%);
    border-top: 2px solid var(--soft-lavender);
}

#user-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    resize: none;
    height: 50px;
    font-size: 1rem;
    outline: none;
    transition: border 0.3s;
}

#user-input:focus {
    border-color: var(--soft-coral);
    box-shadow: 0 0 0 3px rgba(255, 179, 186, 0.2);
}

#send-btn {
    background: linear-gradient(135deg, var(--soft-coral) 0%, var(--soft-lavender) 100%);
    color: var(--warm-white);
    border: none;
    border-radius: 25px;
    padding: 0 20px;
    margin-left: 10px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
}

#send-btn i {
    margin-right: 5px;
}

#send-btn:hover {
    background: linear-gradient(135deg, var(--soft-lavender) 0%, var(--soft-blue) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Estilos para las notas */
.resultado-pedido {
    margin-top: 20px;
}

.notas-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.nota-cliente, .nota-pastelera {
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.nota-cliente {
    border-left: 4px solid var(--pastel-pink);
}

.nota-pastelera {
    border-left: 4px solid var(--pastel-lavender);
}

.nota-cliente h3, .nota-pastelera h3 {
    background: linear-gradient(135deg, var(--pastel-mint) 0%, var(--pastel-yellow) 100%);
    color: var(--soft-purple);
    padding: 15px;
    margin: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nota-cliente pre, .nota-pastelera pre {
    padding: 20px;
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    white-space: pre-wrap;
    color: var(--soft-purple);
    background: linear-gradient(135deg, var(--white) 0%, var(--pastel-blue) 5%);
}

footer {
    text-align: center;
    margin-top: 30px;
    padding: 15px;
    color: var(--soft-purple);
    font-size: 0.9rem;
    border-top: 2px solid var(--pastel-mint);
    background: linear-gradient(135deg, var(--white) 0%, var(--pastel-yellow) 20%);
    border-radius: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        padding: 15px;
    }

    h1 {
        font-size: 1.5rem;
    }

    .pasteleria-logo {
        width: 60px;
        height: 60px;
    }

    .pasteleria-logo i {
        font-size: 2rem;
    }

    .message {
        max-width: 90%;
    }

    .notas-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .chat-container {
        height: 40vh;
    }

    .botones-menu {
        flex-direction: column;
        align-items: center;
    }

    .btn-opcion {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .menu-items-grid {
        grid-template-columns: 1fr;
    }

    .extras-grid {
        grid-template-columns: 1fr;
    }

    .menu-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .bienvenida-section {
        padding: 20px;
    }

    .ubicacion {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.2rem;
    }

    .pasteleria-logo {
        width: 50px;
        height: 50px;
    }

    .pasteleria-logo i {
        font-size: 1.5rem;
    }

    #send-btn span {
        display: none;
    }

    #send-btn i {
        margin-right: 0;
    }

    .cliente-info {
        padding: 15px;
    }

    .input-group {
        gap: 5px;
    }
}
