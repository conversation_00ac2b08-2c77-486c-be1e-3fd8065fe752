"""
Servicio para generar respuestas utilizando OpenRouter para el chatbot de pastelería.
"""

import json
import httpx
from ..utils.config import settings
from .pasteleria_service import PasteleriaService

class AIService:
    """
    Clase que implementa el servicio de respuestas basadas en IA para la pastelería.
    """

    # Variables para el servicio
    pasteleria_service = None
    initialized = False

    @classmethod
    def initialize(cls):
        """
        Inicializa el servicio de IA para la pastelería.
        """
        if cls.initialized:
            return

        try:
            # Inicializar el servicio de pastelería
            PasteleriaService.initialize()
            cls.pasteleria_service = PasteleriaService.get_instance()
            cls.initialized = True
            print("Servicio de IA para pastelería inicializado correctamente.")

        except Exception as e:
            print(f"Error al inicializar el servicio de IA para pastelería: {str(e)}")
            cls.pasteleria_service = None

    @classmethod
    async def get_response(cls, question: str, nombre_cliente: str = "Cliente") -> dict:
        """
        Procesa un pedido de pastelería usando IA y genera las notas correspondientes.

        Args:
            question: El pedido del usuario.
            nombre_cliente: Nombre del cliente.

        Returns:
            Diccionario con la información del pedido procesado y las notas.
        """
        try:
            # Inicializar el servicio si no se ha hecho
            if not cls.initialized:
                cls.initialize()

            # Verificar que el servicio esté disponible
            if cls.pasteleria_service is None:
                return {
                    "error": "Error: No se ha podido cargar el servicio de pastelería.",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

            # Verificar que la clave API esté configurada
            if not settings.OPENROUTER_API_KEY or settings.OPENROUTER_API_KEY == "your_openrouter_api_key_here":
                print("❌ API Key no configurada")
                return {
                    "error": "OpenRouter API key no configurada. Por favor configura la API key en el archivo .env",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

            print(f"✅ API Key configurada: {settings.OPENROUTER_API_KEY[:10]}...")

            # Usar IA para procesar el pedido
            return await cls._process_with_ai(question, nombre_cliente)

        except Exception as e:
            error_message = f"Error en el servicio de IA: {str(e)}"
            print(error_message)
            return {
                "error": "Lo siento, ha ocurrido un error al procesar tu pedido. Por favor, intenta de nuevo más tarde.",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }

    @classmethod
    async def _process_with_ai(cls, question: str, nombre_cliente: str) -> dict:
        """Procesar pedido usando OpenRouter AI."""
        try:
            # Obtener información de la base de datos
            pasteles = cls.pasteleria_service.obtener_todos_pasteles()
            extras = cls.pasteleria_service.obtener_todos_extras()

            # Crear contexto simplificado para la IA
            menu_info = "PASTELES DISPONIBLES:\n"
            for nombre, datos in pasteles.items():
                menu_info += f"- {nombre}: ${datos['precio']} ({datos['rebanadas']} rebanadas)\n"

            menu_info += "\nEXTRAS (todos $40): fresas, chocolate, nueces, caramelo, crema, dulce de leche, almendras, frambuesas\n"

            # Headers simplificados para OpenRouter
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                "HTTP-Referer": "http://localhost:8000"
            }

            # Mensaje del sistema mejorado y más específico
            system_message = f"""Eres asistente de Pastelería Victoria's. Procesa pedidos de pasteles.

{menu_info}

EXTRAS DISPONIBLES (todos $40): fresas, chocolate extra, nueces, caramelo, crema extra, dulce de leche, almendras, frambuesas, moras, durazno, plátano, kiwi

INSTRUCCIONES ESTRICTAS:
1. Identifica el pastel solicitado (si no especifica tamaño, usa el mediano de 12 rebanadas)
2. Identifica extras mencionados (cada extra cuesta $40)
3. Calcula precio total = precio_pastel + (cantidad_extras * 40)
4. Responde ÚNICAMENTE en formato JSON válido:

{{"pastel_detectado": "nombre_exacto", "precio_pastel": precio, "extras_detectados": ["lista"], "precio_extras": total_extras, "precio_total": total, "mensaje": "Pedido confirmado"}}

Si no encuentras el pastel:
{{"error": "No identifiqué el pastel solicitado. Revisa nuestro menú disponible."}}

IMPORTANTE: Responde SOLO JSON, sin texto adicional."""

            # Payload simplificado
            payload = {
                "model": settings.AI_MODEL,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": question}
                ],
                "temperature": 0.2,
                "max_tokens": 300
            }

            print(f"🤖 Enviando a OpenRouter: {question}")

            # Hacer solicitud a OpenRouter
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    settings.OPENROUTER_URL,
                    headers=headers,
                    json=payload
                )

                print(f"📡 Respuesta OpenRouter: {response.status_code}")

                if response.status_code == 200:
                    response_data = response.json()
                    print(f"📡 Respuesta completa de OpenRouter: {response_data}")

                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        ai_response = response_data["choices"][0]["message"]["content"]
                        print(f"🎯 IA respuesta extraída: {ai_response}")

                        # Procesar respuesta de la IA
                        return await cls._process_ai_response(ai_response, nombre_cliente, question)
                    else:
                        print(f"❌ Respuesta sin choices: {response_data}")
                        return {
                            "error": "Respuesta inválida del servicio de IA",
                            "pedido": None,
                            "nota_cliente": None,
                            "nota_pastelera": None
                        }
                else:
                    error_text = response.text
                    print(f"❌ Error OpenRouter: {response.status_code} - {error_text}")

                    # Intentar procesar el error para dar más información
                    try:
                        error_data = response.json()
                        error_message = error_data.get("error", {}).get("message", "Error desconocido")
                        print(f"❌ Detalle del error: {error_message}")
                    except:
                        error_message = f"Error HTTP {response.status_code}"

                    return {
                        "error": f"Error del servicio de IA: {error_message}",
                        "pedido": None,
                        "nota_cliente": None,
                        "nota_pastelera": None
                    }

        except Exception as e:
            print(f"❌ Error en procesamiento con IA: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": f"Error procesando con IA: {str(e)}",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }



    @classmethod
    async def _process_ai_response(cls, ai_response: str, nombre_cliente: str, question: str = "") -> dict:
        """Procesar la respuesta de la IA y generar las notas."""
        try:
            import json

            # Limpiar la respuesta y extraer JSON
            ai_response = ai_response.strip()
            print(f"🔍 Respuesta original: {ai_response}")

            # Limpiar respuesta de bloques de código
            if "```" in ai_response:
                parts = ai_response.split("```")
                for part in parts:
                    if "{" in part and "}" in part:
                        ai_response = part.strip()
                        break

            # Buscar el JSON en la respuesta
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}')

            if start_idx == -1 or end_idx == -1:
                # Si no se encuentra JSON completo, intentar crear uno básico
                print("⚠️ JSON incompleto, intentando extraer información...")
                if "chocolate" in ai_response.lower():
                    ai_data = {
                        "pastel_detectado": "chocolate 3 leches",
                        "precio_pastel": 530,
                        "extras_detectados": [],
                        "precio_extras": 0,
                        "precio_total": 530,
                        "mensaje": "Pedido procesado (respuesta incompleta)"
                    }
                else:
                    raise ValueError("No se encontró JSON válido en la respuesta")
            else:
                json_str = ai_response[start_idx:end_idx+1]
                print(f"🔍 JSON extraído: {json_str}")
                ai_data = json.loads(json_str)
            print(f"🔍 Datos parseados: {ai_data}")

            if "error" in ai_data:
                return {
                    "error": ai_data["error"],
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

            # Validar campos requeridos
            required_fields = ["pastel_detectado", "precio_pastel", "extras_detectados", "precio_total"]
            for field in required_fields:
                if field not in ai_data:
                    raise ValueError(f"Campo requerido '{field}' no encontrado en respuesta IA")

            # Obtener información del pastel desde la base de datos
            pasteles = cls.pasteleria_service.obtener_todos_pasteles()
            pastel_nombre = ai_data["pastel_detectado"].lower()

            # Buscar el pastel en la base de datos
            pastel_info = None
            for nombre, datos in pasteles.items():
                if nombre.lower() == pastel_nombre:
                    pastel_info = datos
                    break

            if not pastel_info:
                # Buscar coincidencias parciales
                for nombre, datos in pasteles.items():
                    if pastel_nombre in nombre.lower() or nombre.lower() in pastel_nombre:
                        pastel_info = datos
                        ai_data["pastel_detectado"] = nombre
                        break

            if not pastel_info:
                return {
                    "error": f"No se encontró el pastel '{ai_data['pastel_detectado']}' en la base de datos.",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

            # Crear objetos de pastel y extras
            pastel_obj = {
                "nombre": ai_data["pastel_detectado"],
                "precio": ai_data["precio_pastel"],
                "tipo": pastel_info["tipo"],
                "rebanadas": pastel_info["rebanadas"]
            }

            extras_obj = [{"nombre": extra, "precio": 40} for extra in ai_data["extras_detectados"]]

            # Calcular insumos necesarios
            try:
                insumos = cls.pasteleria_service._calcular_insumos(pastel_obj, extras_obj)
            except Exception as e:
                print(f"⚠️ Error calculando insumos: {e}")
                insumos = []

            # Crear estructura de pedido
            pedido = {
                "pastel": pastel_obj,
                "extras": extras_obj,
                "precio_total": ai_data["precio_total"],
                "insumos_necesarios": insumos,
                "error": None
            }

            # Generar las notas
            nota_cliente = cls.pasteleria_service.generar_nota_cliente(pedido, nombre_cliente)
            nota_pastelera = cls.pasteleria_service.generar_nota_pastelera(pedido, nombre_cliente)

            print(f"✅ Pedido procesado con IA: ${ai_data['precio_total']}")
            return {
                "error": None,
                "pedido": pedido,
                "nota_cliente": nota_cliente,
                "nota_pastelera": nota_pastelera,
                "mensaje": ai_data.get("mensaje", f"Pedido procesado exitosamente. Total: ${ai_data['precio_total']}")
            }

        except Exception as e:
            print(f"❌ Error procesando respuesta IA: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": f"Error procesando respuesta de IA: {str(e)}",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }
