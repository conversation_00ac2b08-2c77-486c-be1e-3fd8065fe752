"""
Servicio para generar respuestas utilizando OpenRouter para el chatbot de pastelería.
"""

import json
import httpx
from datetime import datetime, timedelta
from ..utils.config import settings
from .pasteleria_service import PasteleriaService
from .inventario_service import InventarioService
from .notas_service import NotasService

class AIService:
    """
    Clase que implementa el servicio de respuestas basadas en IA para la pastelería.
    """

    # Variables para los servicios
    pasteleria_service = None
    inventario_service = None
    notas_service = None
    initialized = False

    @classmethod
    def initialize(cls):
        """
        Inicializa el servicio de IA para la pastelería.
        """
        if cls.initialized:
            return

        try:
            # Inicializar todos los servicios
            PasteleriaService.initialize()
            InventarioService.initialize()

            cls.pasteleria_service = PasteleriaService.get_instance()
            cls.inventario_service = InventarioService.get_instance()
            cls.notas_service = NotasService()

            cls.initialized = True
            print("✅ Servicios de IA inicializados correctamente")

        except Exception as e:
            print(f"❌ Error al inicializar servicios: {str(e)}")
            cls.pasteleria_service = None
            cls.inventario_service = None
            cls.notas_service = None

    @classmethod
    async def get_response(cls, question: str, nombre_cliente: str = "Cliente") -> dict:
        """
        Procesa un pedido de pastelería usando IA y genera las notas correspondientes.
        Si la IA falla, usa un sistema de respaldo basado en reglas.

        Args:
            question: El pedido del usuario.
            nombre_cliente: Nombre del cliente.

        Returns:
            Diccionario con la información del pedido procesado y las notas.
        """
        try:
            # Inicializar el servicio si no se ha hecho
            if not cls.initialized:
                cls.initialize()

            # Verificar que el servicio esté disponible
            if cls.pasteleria_service is None:
                return {
                    "error": "Error: No se ha podido cargar el servicio de pastelería.",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

            # Verificar comando especial "restablecer"
            if question.lower().strip() == "restablecer":
                cls.inventario_service.restablecer_inventario()
                return {
                    "error": None,
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None,
                    "mensaje": "✅ Inventario restablecido a valores iniciales",
                    "tipo": "sistema"
                }

            # Intentar usar IA primero
            if settings.OPENROUTER_API_KEY and settings.OPENROUTER_API_KEY != "your_openrouter_api_key_here":
                print(f"✅ Intentando procesar con IA...")
                try:
                    resultado_ia = await cls._process_with_ai(question, nombre_cliente)
                    if not resultado_ia.get("error"):
                        print("✅ Procesado exitosamente con IA")
                        return resultado_ia
                    else:
                        print(f"⚠️ IA falló: {resultado_ia['error']}")
                except Exception as e:
                    print(f"⚠️ Error en IA: {e}")

            # Si la IA falla, usar sistema de respaldo
            print("🔄 Usando sistema de respaldo...")
            return cls._process_with_fallback(question, nombre_cliente)

        except Exception as e:
            error_message = f"Error en el servicio de IA: {str(e)}"
            print(error_message)
            # Como último recurso, usar el sistema de respaldo
            try:
                return cls._process_with_fallback(question, nombre_cliente)
            except:
                return {
                    "error": "Lo siento, ha ocurrido un error al procesar tu pedido. Por favor, intenta de nuevo más tarde.",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

    @classmethod
    async def _process_with_ai(cls, question: str, nombre_cliente: str) -> dict:
        """Procesar pedido usando OpenRouter AI."""
        try:
            # Obtener información de la base de datos
            pasteles = cls.pasteleria_service.obtener_todos_pasteles()
            extras = cls.pasteleria_service.obtener_todos_extras()

            # Crear contexto simplificado para la IA
            menu_info = "PASTELES DISPONIBLES:\n"
            for nombre, datos in pasteles.items():
                menu_info += f"- {nombre}: ${datos['precio']} ({datos['rebanadas']} rebanadas)\n"

            menu_info += "\nEXTRAS (todos $40): fresas, chocolate, nueces, caramelo, crema, dulce de leche, almendras, frambuesas\n"

            # Headers simplificados para OpenRouter
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                "HTTP-Referer": "http://localhost:8000"
            }

            # Mensaje del sistema CONVERSACIONAL - PROCESO COMPLETO OBLIGATORIO
            system_message = f"""Eres el asistente conversacional de Pastelería Victoria's. Eres MUY amigable y NUNCA cierras pedidos hasta que el cliente confirme que NO necesita nada más.

PRECIOS EXACTOS DE LA BASE DE DATOS (USA SOLO ESTOS):
{menu_info}

EXTRAS DISPONIBLES (TODOS exactamente $40): fresas, chocolate extra, nueces, caramelo, crema extra, dulce de leche, almendras, frambuesas, moras, durazno, plátano, kiwi

PROCESO OBLIGATORIO (NUNCA SALTES PASOS):
1. Saludo y obtener nombre
2. Preguntar qué pastel desea (tipo y tamaño)
3. Preguntar si desea extras
4. Preguntar si desea otros postres adicionales
5. Preguntar para qué fecha (mínimo 2 días)
6. PREGUNTAR: "¿Necesitas algo más o eso sería todo?"
7. SOLO SI DICE "NO" o "ESO ES TODO" → procesar pedido

REGLAS ESTRICTAS:
- NUNCA inventes precios - USA SOLO los precios exactos de la base de datos
- NUNCA proceses un pedido hasta que el cliente confirme que NO necesita nada más
- SIEMPRE pregunta "¿Necesitas algo más?" antes de cerrar
- Los pedidos requieren 2 días de antelación mínimo
- Mantén conversación hasta confirmación final

TIPOS DE RESPUESTA:

1. SALUDO:
{{"tipo": "conversacion", "mensaje": "¡Hola! Bienvenido a Pastelería Victoria's 🍰 ¿Cuál es tu nombre?"}}

2. RECOPILANDO INFORMACIÓN:
{{"tipo": "conversacion", "mensaje": "pregunta específica para el siguiente paso"}}

3. CONSULTA DE MENÚ:
{{"tipo": "menu", "mensaje": "información del menú"}}

4. CONFIRMACIÓN FINAL (solo cuando cliente dice que no necesita más):
{{"tipo": "pedido_completo", "nombre_cliente": "nombre", "pastel_detectado": "nombre_exacto", "precio_pastel": precio_exacto, "extras_detectados": ["lista"], "precio_extras": total_extras, "precio_total": total_final, "fecha_entrega": "DD/MM/YYYY", "mensaje": "resumen del pedido confirmado"}}

IMPORTANTE:
- Responde SOLO en JSON válido
- USA SOLO precios exactos de la base de datos
- NUNCA uses "pedido_completo" hasta que cliente confirme que no necesita más
- SIEMPRE pregunta "¿Necesitas algo más?" antes de procesar"""

            # Payload simplificado
            payload = {
                "model": settings.AI_MODEL,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": question}
                ],
                "temperature": 0.2,
                "max_tokens": 300
            }

            print(f"🤖 Enviando a OpenRouter: {question}")

            # Hacer solicitud a OpenRouter
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    settings.OPENROUTER_URL,
                    headers=headers,
                    json=payload
                )

                print(f"📡 Respuesta OpenRouter: {response.status_code}")

                if response.status_code == 200:
                    response_data = response.json()
                    print(f"📡 Respuesta OpenRouter status: OK")

                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        ai_response = response_data["choices"][0]["message"]["content"]
                        print(f"🎯 IA respuesta: {ai_response[:100]}...")

                        # Procesar respuesta de la IA
                        return await cls._process_ai_response(ai_response, nombre_cliente)
                    else:
                        print(f"❌ Respuesta sin choices válidos")
                        raise Exception("Respuesta inválida del servicio de IA")
                else:
                    error_text = response.text
                    print(f"❌ Error OpenRouter: {response.status_code}")

                    # Intentar procesar el error para dar más información
                    try:
                        error_data = response.json()
                        error_message = error_data.get("error", {}).get("message", "Error desconocido")
                        print(f"❌ Detalle del error: {error_message}")
                    except:
                        error_message = f"Error HTTP {response.status_code}"

                    raise Exception(f"Error del servicio de IA: {error_message}")

        except Exception as e:
            print(f"❌ Error en procesamiento con IA: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": f"Error procesando con IA: {str(e)}",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }

    @classmethod
    def _process_with_fallback(cls, question: str, nombre_cliente: str) -> dict:
        """
        Sistema de respaldo que procesa pedidos sin IA usando reglas básicas.
        """
        try:
            print(f"🔄 Procesando pedido con sistema de respaldo: {question}")

            # Usar el procesamiento básico del servicio de pastelería
            resultado = cls.pasteleria_service.procesar_pedido(question)

            if resultado["error"]:
                # Si no se encuentra el pastel, intentar sugerir uno por defecto
                print("⚠️ No se encontró pastel, usando chocolate 3 leches por defecto")
                pastel_default = cls.pasteleria_service.buscar_pastel("chocolate 3 leches")
                if pastel_default:
                    resultado = {
                        "pastel": pastel_default,
                        "extras": cls._extract_extras_fallback(question),
                        "precio_total": pastel_default["precio"],
                        "insumos_necesarios": [],
                        "error": None
                    }

                    # Agregar precio de extras
                    for extra in resultado["extras"]:
                        resultado["precio_total"] += extra["precio"]

                    # Calcular insumos
                    resultado["insumos_necesarios"] = cls.pasteleria_service._calcular_insumos(
                        resultado["pastel"], resultado["extras"]
                    )
                else:
                    return {
                        "error": "No se pudo procesar el pedido. Por favor especifica el tipo de pastel que deseas.",
                        "pedido": None,
                        "nota_cliente": None,
                        "nota_pastelera": None
                    }

            # Generar las notas
            nota_cliente = cls.pasteleria_service.generar_nota_cliente(resultado, nombre_cliente)
            nota_pastelera = cls.pasteleria_service.generar_nota_pastelera(resultado, nombre_cliente)

            print(f"✅ Pedido procesado con sistema de respaldo: ${resultado['precio_total']}")
            return {
                "error": None,
                "pedido": resultado,
                "nota_cliente": nota_cliente,
                "nota_pastelera": nota_pastelera,
                "mensaje": f"Pedido procesado exitosamente. Total: ${resultado['precio_total']}"
            }

        except Exception as e:
            print(f"❌ Error en sistema de respaldo: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": "Error al procesar el pedido con el sistema de respaldo.",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }

    @classmethod
    def _extract_extras_fallback(cls, question: str) -> list:
        """Extraer extras del texto usando reglas simples."""
        extras_encontrados = []
        question_lower = question.lower()

        # Lista de extras disponibles
        extras_disponibles = {
            "fresas": {"nombre": "fresas", "precio": 40},
            "frambuesas": {"nombre": "frambuesas", "precio": 40},
            "chocolate": {"nombre": "chocolate extra", "precio": 40},
            "nueces": {"nombre": "nueces", "precio": 40},
            "caramelo": {"nombre": "caramelo", "precio": 40},
            "crema": {"nombre": "crema extra", "precio": 40},
            "dulce de leche": {"nombre": "dulce de leche", "precio": 40},
            "almendras": {"nombre": "almendras", "precio": 40},
            "moras": {"nombre": "moras", "precio": 40},
            "durazno": {"nombre": "durazno", "precio": 40},
            "plátano": {"nombre": "plátano", "precio": 40},
            "kiwi": {"nombre": "kiwi", "precio": 40}
        }

        for palabra_clave, extra_info in extras_disponibles.items():
            if palabra_clave in question_lower:
                extras_encontrados.append(extra_info)

        return extras_encontrados

    @classmethod
    async def _process_ai_response(cls, ai_response: str, nombre_cliente: str) -> dict:
        """Procesar la respuesta de la IA y generar las notas."""
        try:
            # Limpiar la respuesta y extraer JSON
            ai_response = ai_response.strip()
            print(f"🔍 Respuesta original: {ai_response}")

            # Limpiar respuesta de bloques de código
            if "```" in ai_response:
                parts = ai_response.split("```")
                for part in parts:
                    if "{" in part and "}" in part:
                        ai_response = part.strip()
                        break

            # Buscar el JSON en la respuesta
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}')

            if start_idx == -1 or end_idx == -1:
                print("⚠️ JSON incompleto, intentando extraer información...")
                raise ValueError("No se encontró JSON válido en la respuesta")
            else:
                json_str = ai_response[start_idx:end_idx+1]
                print(f"🔍 JSON extraído: {json_str}")
                try:
                    ai_data = json.loads(json_str)
                except json.JSONDecodeError as e:
                    print(f"❌ Error parseando JSON: {e}")
                    raise ValueError(f"JSON inválido: {e}")

            print(f"🔍 Datos parseados: {ai_data}")

            # Manejar diferentes tipos de respuesta
            tipo_respuesta = ai_data.get("tipo", "conversacion")

            if tipo_respuesta == "conversacion":
                return {
                    "error": None,
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None,
                    "mensaje": ai_data.get("mensaje", "¡Hola! ¿En qué puedo ayudarte hoy?"),
                    "tipo": "conversacion"
                }

            elif tipo_respuesta == "menu":
                return {
                    "error": None,
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None,
                    "mensaje": ai_data.get("mensaje", "Aquí tienes información de nuestro menú"),
                    "tipo": "menu"
                }

            elif tipo_respuesta == "fecha_invalida":
                return {
                    "error": None,
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None,
                    "mensaje": ai_data.get("mensaje", "Los pedidos requieren 2 días de antelación mínimo. ¿Para qué fecha te gustaría el pastel?"),
                    "tipo": "fecha_invalida"
                }

            elif tipo_respuesta == "aclaracion":
                return {
                    "error": None,
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None,
                    "mensaje": ai_data.get("mensaje", "¿Podrías darme más detalles sobre tu pedido?"),
                    "tipo": "aclaracion"
                }

            elif tipo_respuesta == "pedido":
                # Procesar pedido (pregunta fecha pero no confirma aún)
                return await cls._process_pedido_parcial(ai_data, nombre_cliente)

            elif tipo_respuesta == "pedido_completo":
                # Procesar pedido completo (solo cuando se tiene toda la información)
                return await cls._process_pedido_completo(ai_data, ai_data.get("nombre_cliente", nombre_cliente))

            else:
                return {
                    "error": "Tipo de respuesta no reconocido",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

        except Exception as e:
            print(f"❌ Error procesando respuesta IA: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": f"Error procesando respuesta de IA: {str(e)}",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }

    @classmethod
    async def _process_pedido_parcial(cls, ai_data: dict, nombre_cliente: str) -> dict:
        """Procesar un pedido parcial (solo pregunta fecha, no confirma)."""
        try:
            # Solo mostrar información del pedido sin procesar completamente
            return {
                "error": None,
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None,
                "mensaje": ai_data.get("mensaje", "¿Para qué fecha necesitas tu pastel?"),
                "tipo": "pedido_parcial",
                "pastel_detectado": ai_data.get("pastel_detectado"),
                "precio_total": ai_data.get("precio_total")
            }
        except Exception as e:
            print(f"❌ Error procesando pedido parcial: {e}")
            return {
                "error": f"Error procesando pedido: {str(e)}",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }

    @classmethod
    async def _process_pedido_completo(cls, ai_data: dict, nombre_cliente: str) -> dict:
        """Procesar un pedido completo con validación de inventario y guardado de notas."""
        try:
            # Validar campos requeridos para pedido completo
            required_fields = ["nombre_cliente", "pastel_detectado", "precio_pastel", "extras_detectados", "precio_total", "fecha_entrega"]
            for field in required_fields:
                if field not in ai_data:
                    raise ValueError(f"Campo requerido '{field}' no encontrado en respuesta IA")

            # Usar el nombre del cliente de la IA
            nombre_cliente = ai_data["nombre_cliente"]

            # Obtener información del pastel desde la base de datos
            pasteles = cls.pasteleria_service.obtener_todos_pasteles()
            pastel_nombre = ai_data["pastel_detectado"].lower()

            # Buscar el pastel en la base de datos
            pastel_info = None
            for nombre, datos in pasteles.items():
                if nombre.lower() == pastel_nombre:
                    pastel_info = datos
                    break

            if not pastel_info:
                # Buscar coincidencias parciales
                for nombre, datos in pasteles.items():
                    if pastel_nombre in nombre.lower() or nombre.lower() in pastel_nombre:
                        pastel_info = datos
                        ai_data["pastel_detectado"] = nombre
                        break

            if not pastel_info:
                return {
                    "error": f"No se encontró el pastel '{ai_data['pastel_detectado']}' en la base de datos.",
                    "pedido": None,
                    "nota_cliente": None,
                    "nota_pastelera": None
                }

            # Crear objetos de pastel y extras
            pastel_obj = {
                "nombre": ai_data["pastel_detectado"],
                "precio": ai_data["precio_pastel"],
                "tipo": pastel_info["tipo"],
                "rebanadas": pastel_info["rebanadas"]
            }

            extras_obj = [{"nombre": extra, "precio": 40} for extra in ai_data["extras_detectados"]]

            # Verificar disponibilidad de inventario
            disponible, faltantes = cls.inventario_service.verificar_disponibilidad(pastel_obj, extras_obj)

            # Calcular insumos necesarios
            try:
                insumos = cls.pasteleria_service._calcular_insumos(pastel_obj, extras_obj)
            except Exception as e:
                print(f"⚠️ Error calculando insumos: {e}")
                insumos = []

            # Crear estructura de pedido
            pedido = {
                "pastel": pastel_obj,
                "extras": extras_obj,
                "precio_total": ai_data["precio_total"],
                "insumos_necesarios": insumos,
                "error": None,
                "fecha_entrega": ai_data.get("fecha_entrega", ""),
                "inventario_disponible": disponible,
                "faltantes": faltantes
            }

            # Generar las notas (incluyendo alertas de inventario)
            nota_cliente = cls.pasteleria_service.generar_nota_cliente(pedido, nombre_cliente)
            nota_pastelera = cls.pasteleria_service.generar_nota_pastelera(pedido, nombre_cliente)

            # Agregar alertas de inventario a la nota de la pastelera
            if faltantes:
                nota_pastelera += "\n\n⚠️ ALERTAS DE INVENTARIO:\n"
                nota_pastelera += "=" * 30 + "\n"
                for faltante in faltantes:
                    nota_pastelera += f"• {faltante}\n"

            # Descontar insumos del inventario si está disponible
            if disponible:
                cls.inventario_service.descontar_insumos(pastel_obj, extras_obj)
                print("✅ Insumos descontados del inventario")

            # Guardar las notas en archivos
            resultado_guardado = cls.notas_service.guardar_notas_pedido(
                pedido, nota_cliente, nota_pastelera, nombre_cliente
            )

            mensaje_final = ai_data.get("mensaje", f"Pedido procesado exitosamente. Total: ${ai_data['precio_total']}")
            if not disponible:
                mensaje_final += f"\n⚠️ Nota: Faltan algunos insumos para este pedido."

            print(f"✅ Pedido procesado con IA: ${ai_data['precio_total']}")
            return {
                "error": None,
                "pedido": pedido,
                "nota_cliente": nota_cliente,
                "nota_pastelera": nota_pastelera,
                "mensaje": mensaje_final,
                "archivos_guardados": resultado_guardado.get("guardado", False)
            }

        except Exception as e:
            print(f"❌ Error procesando pedido completo: {e}")
            import traceback
            traceback.print_exc()
            return {
                "error": f"Error procesando pedido: {str(e)}",
                "pedido": None,
                "nota_cliente": None,
                "nota_pastelera": None
            }