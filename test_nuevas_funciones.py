#!/usr/bin/env python3
"""
Test de las nuevas funcionalidades del sistema de pastelería
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_nuevas_funciones():
    """Test de las nuevas funcionalidades"""

    print("🧪 TEST DE NUEVAS FUNCIONALIDADES")
    print("=" * 50)

    try:
        from backend.app.services.ai_service import AIService
        from backend.app.services.inventario_service import InventarioService
        from backend.app.services.notas_service import NotasService

        # Inicializar servicios
        AIService.initialize()
        print("✅ Servicios inicializados")

        # Test 1: Comando restablecer
        print("\n🔄 Test 1: Comando 'restablecer'")
        print("-" * 30)
        resultado = await AIService.get_response("restablecer", "Admin")
        print(f"Resultado: {resultado['mensaje']}")

        # Test 2: Saludo conversacional
        print("\n👋 Test 2: Saludo conversacional")
        print("-" * 30)
        resultado = await AIService.get_response("hola", "Juan")
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")

        # Test 3: Consulta de menú
        print("\n📋 Test 3: Consulta de menú")
        print("-" * 30)
        resultado = await AIService.get_response("qué pasteles tienen", "María")
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')[:100]}...")

        # Test 4: Pedido específico
        print("\n🍰 Test 4: Pedido específico")
        print("-" * 30)
        resultado = await AIService.get_response("quiero un pastel de chocolate 3 leches mediano", "Cliente Test")

        if resultado.get("error"):
            print(f"❌ Error: {resultado['error']}")
        elif resultado.get("pedido"):
            print(f"✅ Pedido procesado")
            print(f"💰 Precio: ${resultado['pedido']['precio_total']}")
            print(f"📁 Archivos guardados: {resultado.get('archivos_guardados', False)}")
        else:
            print(f"💬 Respuesta conversacional: {resultado.get('mensaje', 'N/A')[:100]}...")
            print(f"🔄 Tipo: {resultado.get('tipo', 'N/A')}")

            # Intentar con sistema de respaldo
            print("\n🔄 Probando sistema de respaldo...")
            resultado_respaldo = await AIService._process_with_fallback("pastel de chocolate", "Cliente Test")
            if resultado_respaldo.get("error"):
                print(f"❌ Error en respaldo: {resultado_respaldo['error']}")
            else:
                print(f"✅ Respaldo exitoso: ${resultado_respaldo['pedido']['precio_total']}")

        # Test 5: Verificar inventario
        print("\n📦 Test 5: Estado del inventario")
        print("-" * 30)
        inventario_service = InventarioService.get_instance()
        inventario = inventario_service.obtener_estado_inventario()

        print("Inventario actual:")
        for item, datos in list(inventario.items())[:5]:  # Solo mostrar 5 items
            cantidad = datos['cantidad_actual']
            maxima = datos['cantidad_maxima']
            unidad = datos['unidad']
            print(f"  • {item}: {cantidad}/{maxima} {unidad}")

        # Test 6: Verificar notas guardadas
        print("\n📝 Test 6: Notas guardadas")
        print("-" * 30)
        notas_service = NotasService()
        estadisticas = notas_service.obtener_estadisticas_notas()

        print(f"Total de pedidos guardados: {estadisticas['total_pedidos']}")
        print(f"Total de clientes: {estadisticas['total_clientes']}")
        print(f"Total de ventas: ${estadisticas['total_ventas']}")
        print(f"Directorio: {estadisticas['directorio']}")

        # Test 7: Listar notas recientes
        notas_recientes = notas_service.listar_notas_recientes(3)
        if notas_recientes:
            print(f"\nÚltimas {len(notas_recientes)} notas:")
            for nota in notas_recientes:
                cliente = nota.get('cliente', 'Desconocido')
                timestamp = nota.get('timestamp', 'N/A')
                print(f"  • {cliente} - {timestamp}")

        print("\n" + "=" * 50)
        print("🎉 TODAS LAS NUEVAS FUNCIONES PROBADAS!")
        print("\nFuncionalidades implementadas:")
        print("✅ IA conversacional (saludo, menú, aclaraciones)")
        print("✅ Comando 'restablecer' para inventario")
        print("✅ Control de inventario con cantidades fijas")
        print("✅ Guardado automático de notas en archivos")
        print("✅ Alertas de insumos faltantes")
        print("✅ Descuento automático de inventario")

        return True

    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_nuevas_funciones())
    if not resultado:
        print("\n❌ TESTS FALLARON")
        sys.exit(1)
    else:
        print("\n✅ TESTS EXITOSOS - Nuevas funciones implementadas correctamente")
