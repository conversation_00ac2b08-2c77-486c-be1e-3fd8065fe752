#!/usr/bin/env python3
"""
Test para verificar que la IA NO cierre pedidos prematuramente
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_conversacion_corregida():
    """Test para verificar que la IA mantiene conversación"""
    
    print("🗣️ TEST DE CONVERSACIÓN CORREGIDA")
    print("=" * 50)
    
    try:
        from backend.app.services.ai_service import AIService
        
        # Inicializar servicios
        AIService.initialize()
        print("✅ Servicios inicializados")
        
        # Test 1: Saludo simple - NO debe cerrar pedido
        print("\n👋 Test 1: Saludo simple")
        print("-" * 30)
        resultado = await AIService.get_response("hola", "Usuario")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")
        
        # Verificar que NO sea un pedido
        if resultado.get('tipo') == 'conversacion':
            print("✅ CORRECTO: Es conversación, no pedido")
        else:
            print("❌ ERROR: No debería ser pedido")
        
        # Test 2: Mencionar pastel sin detalles - NO debe cerrar pedido
        print("\n🍰 Test 2: Mencionar pastel sin detalles")
        print("-" * 30)
        resultado = await AIService.get_response("quiero chocolate", "Usuario")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')[:100]}...")
        
        # Verificar que NO sea un pedido cerrado
        if resultado.get('tipo') == 'conversacion':
            print("✅ CORRECTO: Sigue conversando, no cierra pedido")
        else:
            print("❌ ERROR: No debería cerrar pedido aún")
        
        # Test 3: Dar nombre - NO debe cerrar pedido
        print("\n👤 Test 3: Dar nombre")
        print("-" * 30)
        resultado = await AIService.get_response("mi nombre es Juan", "Usuario")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')[:100]}...")
        
        # Verificar que NO sea un pedido
        if resultado.get('tipo') == 'conversacion':
            print("✅ CORRECTO: Continúa conversación")
        else:
            print("❌ ERROR: No debería ser pedido")
        
        # Test 4: Consulta de menú
        print("\n📋 Test 4: Consulta de menú")
        print("-" * 30)
        resultado = await AIService.get_response("qué pasteles tienen", "Usuario")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')[:100]}...")
        
        # Test 5: Comando restablecer
        print("\n🔄 Test 5: Comando restablecer")
        print("-" * 30)
        resultado = await AIService.get_response("restablecer", "Admin")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")
        
        print("\n" + "=" * 50)
        print("🎉 TEST DE CONVERSACIÓN CORREGIDA COMPLETADO!")
        print("\nResultados esperados:")
        print("✅ 'Hola' debe ser conversación, NO pedido")
        print("✅ Mencionar pastel debe seguir conversando")
        print("✅ Dar nombre debe continuar conversación")
        print("✅ Consulta menú debe mostrar información")
        print("✅ Comando restablecer debe funcionar")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_conversacion_corregida())
    if not resultado:
        print("\n❌ TEST FALLÓ")
        sys.exit(1)
    else:
        print("\n✅ TEST EXITOSO - IA ya no cierra pedidos prematuramente")
