#!/usr/bin/env python3
"""
Script simple para probar el servidor de la pastelería.
"""

import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from backend.app.main import app
    print("✅ Importación exitosa del módulo principal")
    
    # Probar la importación de servicios
    from backend.app.services.pasteleria_service import PasteleriaService
    print("✅ Servicio de pastelería importado correctamente")
    
    from backend.app.services.ai_service import AIService
    print("✅ Servicio de AI importado correctamente")
    
    # Inicializar servicios
    PasteleriaService.initialize()
    print("✅ Servicio de pastelería inicializado")
    
    AIService.initialize()
    print("✅ Servicio de AI inicializado")
    
    # Probar el servicio de pastelería
    servicio = PasteleriaService.get_instance()
    pasteles = servicio.obtener_todos_pasteles()
    print(f"✅ Base de datos cargada: {len(pasteles)} pasteles disponibles")
    
    # Probar procesamiento de pedido
    pedido_test = servicio.procesar_pedido("quiero un pastel de chocolate con fresas extra")
    if pedido_test["error"]:
        print(f"❌ Error en procesamiento: {pedido_test['error']}")
    else:
        print(f"✅ Pedido procesado: ${pedido_test['precio_total']}")
    
    print("\n🎉 Todos los tests pasaron correctamente!")
    print("🚀 El servidor debería funcionar sin problemas")
    
except ImportError as e:
    print(f"❌ Error de importación: {e}")
    print("💡 Instala las dependencias: pip install fastapi uvicorn")
except Exception as e:
    print(f"❌ Error inesperado: {e}")
    import traceback
    traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Ejecutando tests del servidor de Pastelería Victoria's...")
    print("=" * 50)
