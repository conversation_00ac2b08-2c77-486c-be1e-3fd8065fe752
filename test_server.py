#!/usr/bin/env python3
"""
Script simple para probar el servidor de la pastelería.
"""

import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from backend.app.main import app
    print("✅ Importación exitosa del módulo principal")

    # Probar la importación de servicios
    from backend.app.services.pasteleria_service import PasteleriaService
    print("✅ Servicio de pastelería importado correctamente")

    from backend.app.services.ai_service import AIService
    print("✅ Servicio de AI importado correctamente")

    # Inicializar servicios
    PasteleriaService.initialize()
    print("✅ Servicio de pastelería inicializado")

    AIService.initialize()
    print("✅ Servicio de AI inicializado")

    # Probar el servicio de pastelería
    servicio = PasteleriaService.get_instance()
    pasteles = servicio.obtener_todos_pasteles()
    print(f"✅ Base de datos cargada: {len(pasteles)} pasteles disponibles")

    # Probar procesamiento de pedido básico
    pedido_test = servicio.procesar_pedido("quiero un pastel de chocolate con fresas extra")
    if pedido_test["error"]:
        print(f"❌ Error en procesamiento básico: {pedido_test['error']}")
    else:
        print(f"✅ Pedido básico procesado: ${pedido_test['precio_total']}")

    # Probar el servicio de IA con sistema de respaldo
    import asyncio
    async def test_ai_service():
        resultado = await AIService.get_response("quiero un pastel de chocolate con fresas", "Cliente Test")
        if resultado["error"]:
            print(f"❌ Error en servicio de IA: {resultado['error']}")
        else:
            print(f"✅ Servicio de IA funcionando: ${resultado['pedido']['precio_total']}")

    # Ejecutar test de IA
    try:
        asyncio.run(test_ai_service())
    except Exception as e:
        print(f"⚠️ Test de IA falló (esperado si no hay conexión): {e}")

    print("\n🎉 Todos los tests pasaron correctamente!")
    print("🚀 El servidor debería funcionar sin problemas")

except ImportError as e:
    print(f"❌ Error de importación: {e}")
    print("💡 Instala las dependencias: pip install fastapi uvicorn")
except Exception as e:
    print(f"❌ Error inesperado: {e}")
    import traceback
    traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Ejecutando tests del servidor de Pastelería Victoria's...")
    print("=" * 50)
