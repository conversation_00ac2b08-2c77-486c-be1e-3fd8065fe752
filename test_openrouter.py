#!/usr/bin/env python3
"""
Test específico de OpenRouter
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_openrouter():
    """Test directo de OpenRouter"""
    
    print("🤖 TEST DIRECTO DE OPENROUTER")
    print("=" * 50)
    
    try:
        from backend.app.services.ai_service import AIService
        from backend.app.utils.config import settings
        
        # Verificar configuración
        print(f"🔑 API Key: {settings.OPENROUTER_API_KEY[:20]}...")
        print(f"🤖 Modelo: {settings.AI_MODEL}")
        print(f"🌐 URL: {settings.OPENROUTER_URL}")
        
        # Inicializar servicios
        AIService.initialize()
        print("✅ Servicios inicializados")
        
        # Test 1: Saludo simple
        print("\n👋 Test 1: Saludo simple")
        print("-" * 30)
        resultado = await AIService.get_response("hola", "TestUser")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")
        print(f"Error: {resultado.get('error', 'Ninguno')}")
        
        # Test 2: Pedido directo
        print("\n🍰 Test 2: Pedido directo")
        print("-" * 30)
        resultado = await AIService.get_response("quiero un pastel de chocolate", "TestUser")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')[:100]}...")
        print(f"Error: {resultado.get('error', 'Ninguno')}")
        
        if resultado.get('pastel_detectado'):
            print(f"Pastel detectado: {resultado['pastel_detectado']}")
            print(f"Precio total: ${resultado.get('precio_total', 'N/A')}")
        
        # Test 3: Consulta de menú
        print("\n📋 Test 3: Consulta de menú")
        print("-" * 30)
        resultado = await AIService.get_response("qué pasteles tienen", "TestUser")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')[:100]}...")
        print(f"Error: {resultado.get('error', 'Ninguno')}")
        
        # Test 4: Comando restablecer
        print("\n🔄 Test 4: Comando restablecer")
        print("-" * 30)
        resultado = await AIService.get_response("restablecer", "Admin")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")
        print(f"Error: {resultado.get('error', 'Ninguno')}")
        
        print("\n" + "=" * 50)
        print("🎉 TEST DE OPENROUTER COMPLETADO!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test de OpenRouter: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_openrouter())
    if not resultado:
        print("\n❌ TEST DE OPENROUTER FALLÓ")
        sys.exit(1)
    else:
        print("\n✅ TEST DE OPENROUTER EXITOSO")
