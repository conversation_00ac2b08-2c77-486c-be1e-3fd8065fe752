"""
Servicio para guardar las notas de pedidos en archivos.
"""

import os
import json
from datetime import datetime
from typing import Dict

class NotasService:
    """Servicio para gestionar el guardado de notas de pedidos."""

    def __init__(self):
        self.notas_dir = "backend/data/notas_pedidos"
        self.pedidos_dir = "backend/data/Pedidos"  # Para notas de pastelera
        self.crear_directorios()

    def crear_directorios(self):
        """Crear los directorios para las notas si no existen."""
        try:
            os.makedirs(self.notas_dir, exist_ok=True)
            os.makedirs(self.pedidos_dir, exist_ok=True)
            print(f"✅ Directorio de notas: {self.notas_dir}")
            print(f"✅ Directorio de pedidos: {self.pedidos_dir}")
        except Exception as e:
            print(f"❌ Error creando directorios: {e}")

    def guardar_notas_pedido(self, pedido_data: dict, nota_cliente: str, nota_pastelera: str, nombre_cliente: str) -> dict:
        """
        Guardar las notas de un pedido en archivos separados.

        Args:
            pedido_data: Información del pedido
            nota_cliente: Nota para el cliente
            nota_pastelera: Nota para la pastelera
            nombre_cliente: Nombre del cliente

        Returns:
            Dict con información de los archivos guardados
        """
        try:
            # Generar timestamp para nombres únicos
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            cliente_limpio = self._limpiar_nombre_archivo(nombre_cliente)

            # Nombres de archivos
            archivo_cliente = f"cliente_{cliente_limpio}_{timestamp}.txt"
            archivo_pastelera = f"pastelera_{cliente_limpio}_{timestamp}.txt"
            archivo_json = f"pedido_{cliente_limpio}_{timestamp}.json"

            # Rutas completas - nota de pastelera va a carpeta "Pedidos"
            ruta_cliente = os.path.join(self.notas_dir, archivo_cliente)
            ruta_pastelera = os.path.join(self.pedidos_dir, archivo_pastelera)  # Carpeta Pedidos
            ruta_json = os.path.join(self.notas_dir, archivo_json)

            # Guardar nota del cliente
            with open(ruta_cliente, 'w', encoding='utf-8') as f:
                f.write(nota_cliente)

            # Guardar nota de la pastelera
            with open(ruta_pastelera, 'w', encoding='utf-8') as f:
                f.write(nota_pastelera)

            # Guardar datos completos del pedido en JSON
            pedido_completo = {
                "timestamp": timestamp,
                "fecha_pedido": datetime.now().strftime("%d/%m/%Y %H:%M"),
                "cliente": nombre_cliente,
                "pedido_data": pedido_data,
                "nota_cliente": nota_cliente,
                "nota_pastelera": nota_pastelera,
                "archivos": {
                    "cliente": archivo_cliente,
                    "pastelera": archivo_pastelera,
                    "json": archivo_json
                }
            }

            with open(ruta_json, 'w', encoding='utf-8') as f:
                json.dump(pedido_completo, f, indent=2, ensure_ascii=False)

            print(f"✅ Notas guardadas para {nombre_cliente}")
            print(f"   📄 Cliente: {archivo_cliente}")
            print(f"   👩‍🍳 Pastelera: {archivo_pastelera}")
            print(f"   📊 JSON: {archivo_json}")

            return {
                "guardado": True,
                "archivos": {
                    "cliente": ruta_cliente,
                    "pastelera": ruta_pastelera,
                    "json": ruta_json
                },
                "timestamp": timestamp
            }

        except Exception as e:
            print(f"❌ Error guardando notas: {e}")
            return {
                "guardado": False,
                "error": str(e),
                "timestamp": None
            }

    def _limpiar_nombre_archivo(self, nombre: str) -> str:
        """Limpiar nombre para usar como nombre de archivo."""
        # Remover caracteres no válidos para nombres de archivo
        caracteres_invalidos = '<>:"/\\|?*'
        nombre_limpio = nombre

        for char in caracteres_invalidos:
            nombre_limpio = nombre_limpio.replace(char, '_')

        # Remover espacios extra y convertir a minúsculas
        nombre_limpio = '_'.join(nombre_limpio.split()).lower()

        # Limitar longitud
        if len(nombre_limpio) > 20:
            nombre_limpio = nombre_limpio[:20]

        return nombre_limpio if nombre_limpio else "cliente"

    def listar_notas_recientes(self, limite: int = 10) -> list:
        """Listar las notas más recientes."""
        try:
            archivos_json = []

            # Buscar archivos JSON en el directorio
            for archivo in os.listdir(self.notas_dir):
                if archivo.startswith("pedido_") and archivo.endswith(".json"):
                    ruta_completa = os.path.join(self.notas_dir, archivo)
                    try:
                        with open(ruta_completa, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            data['archivo'] = archivo
                            archivos_json.append(data)
                    except:
                        continue

            # Ordenar por timestamp (más reciente primero)
            archivos_json.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

            return archivos_json[:limite]

        except Exception as e:
            print(f"❌ Error listando notas: {e}")
            return []

    def obtener_estadisticas_notas(self) -> dict:
        """Obtener estadísticas de las notas guardadas."""
        try:
            total_notas = 0
            total_clientes = set()
            total_ventas = 0.0

            for archivo in os.listdir(self.notas_dir):
                if archivo.startswith("pedido_") and archivo.endswith(".json"):
                    ruta_completa = os.path.join(self.notas_dir, archivo)
                    try:
                        with open(ruta_completa, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            total_notas += 1
                            total_clientes.add(data.get('cliente', 'Desconocido'))

                            # Sumar ventas
                            pedido_data = data.get('pedido_data', {})
                            precio_total = pedido_data.get('precio_total', 0)
                            if isinstance(precio_total, (int, float)):
                                total_ventas += precio_total
                    except:
                        continue

            return {
                "total_pedidos": total_notas,
                "total_clientes": len(total_clientes),
                "total_ventas": total_ventas,
                "directorio": self.notas_dir
            }

        except Exception as e:
            print(f"❌ Error obteniendo estadísticas: {e}")
            return {
                "total_pedidos": 0,
                "total_clientes": 0,
                "total_ventas": 0.0,
                "error": str(e)
            }
