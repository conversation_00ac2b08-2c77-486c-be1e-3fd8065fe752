#!/usr/bin/env python3
"""
Test final para verificar que todo funciona antes de lanzar el servidor
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_completo():
    """Test completo del sistema"""
    
    print("🧪 TEST FINAL - Pastelería Victoria's")
    print("=" * 50)
    
    try:
        # Importar módulos
        from backend.app.services.ai_service import AIService
        from backend.app.services.pasteleria_service import PasteleriaService
        from backend.app.utils.config import settings
        
        print("✅ Importaciones exitosas")
        
        # Inicializar servicios
        PasteleriaService.initialize()
        AIService.initialize()
        print("✅ Servicios inicializados")
        
        # Test de configuración
        print(f"🔑 API Key: {'✅ Configurada' if settings.OPENROUTER_API_KEY else '❌ No configurada'}")
        print(f"🤖 Modelo: {settings.AI_MODEL}")
        
        # Test de base de datos
        servicio = PasteleriaService.get_instance()
        pasteles = servicio.obtener_todos_pasteles()
        extras = servicio.obtener_todos_extras()
        print(f"📊 Base de datos: {len(pasteles)} pasteles, {len(extras)} extras")
        
        # Test de pedidos variados
        pedidos_test = [
            "pastel de chocolate",
            "vainilla con fresas",
            "3 leches grande con nueces",
            "algo de chocolate con caramelo",
            "quiero un postre"
        ]
        
        print("\n🔄 Probando diferentes pedidos:")
        print("-" * 30)
        
        for i, pedido in enumerate(pedidos_test, 1):
            try:
                resultado = await AIService.get_response(pedido, f"Cliente{i}")
                
                if resultado.get("error"):
                    print(f"{i}. ❌ '{pedido}' -> {resultado['error']}")
                else:
                    precio = resultado['pedido']['precio_total']
                    print(f"{i}. ✅ '{pedido}' -> ${precio}")
                    
            except Exception as e:
                print(f"{i}. ❌ '{pedido}' -> Error: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 TODOS LOS TESTS COMPLETADOS!")
        print("🚀 El sistema está listo para usar")
        print("\nPara iniciar el servidor ejecuta:")
        print("   py run_server.py")
        print("\nLuego abre tu navegador en:")
        print("   http://localhost:8000")
        
        return True
        
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_completo())
    if not resultado:
        print("\n❌ TESTS FALLARON - Revisa los errores antes de continuar")
        sys.exit(1)
    else:
        print("\n✅ TESTS EXITOSOS - Sistema listo para usar")
