"""
Servicio para manejar el inventario de insumos de la pastelería.
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Tuple

class InventarioService:
    """Servicio para gestionar el inventario de insumos."""
    
    _instance = None
    _initialized = False
    
    def __init__(self):
        self.inventario_file = "backend/data/inventario_actual.json"
        self.inventario_inicial_file = "backend/data/insumos.json"
        self.inventario = {}
        self.recetas_fijas = {
            # Cantidades fijas por tamaño de pastel
            "mini": {
                "Harina de Trigo": "100g",
                "Cartera de Huevo": "1 huevo",
                "Azúcar Regular": "50g",
                "Mantequilla": "25g",
                "Litro de Vainilla": "5ml"
            },
            "chico": {
                "Harina de Trigo": "200g", 
                "Cartera de Huevo": "1 huevo",
                "Azúcar Regular": "75g",
                "Mantequilla": "50g",
                "Litro de Vainilla": "10ml"
            },
            "mediano": {
                "Harina de Trigo": "800g",
                "<PERSON><PERSON> de Huevo": "4 huevos", 
                "Azúcar Regular": "300g",
                "Mantequilla": "150g",
                "Litro de Vainilla": "30ml"
            },
            "grande": {
                "Harina de Trigo": "1200g",
                "Cartera de Huevo": "6 huevos",
                "Azúcar Regular": "450g", 
                "Mantequilla": "225g",
                "Litro de Vainilla": "45ml"
            }
        }
    
    @classmethod
    def get_instance(cls):
        """Obtener la instancia singleton."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    def initialize(cls):
        """Inicializar el servicio."""
        if not cls._initialized:
            instance = cls.get_instance()
            instance.cargar_inventario()
            cls._initialized = True
    
    def cargar_inventario(self):
        """Cargar el inventario actual o crear uno nuevo."""
        try:
            if os.path.exists(self.inventario_file):
                with open(self.inventario_file, 'r', encoding='utf-8') as f:
                    self.inventario = json.load(f)
                print("✅ Inventario actual cargado")
            else:
                self.restablecer_inventario()
                print("✅ Inventario inicial creado")
        except Exception as e:
            print(f"❌ Error cargando inventario: {e}")
            self.restablecer_inventario()
    
    def restablecer_inventario(self):
        """Restablecer el inventario a los valores iniciales."""
        try:
            with open(self.inventario_inicial_file, 'r', encoding='utf-8') as f:
                insumos_iniciales = json.load(f)
            
            # Convertir a formato de inventario actual
            self.inventario = {}
            for insumo in insumos_iniciales:
                self.inventario[insumo["nombre"]] = {
                    "cantidad_actual": insumo["cantidad"],
                    "cantidad_maxima": insumo["cantidad"],
                    "unidad": insumo["unidad"],
                    "precio": insumo["precio"]
                }
            
            self.guardar_inventario()
            print("✅ Inventario restablecido a valores iniciales")
            
        except Exception as e:
            print(f"❌ Error restableciendo inventario: {e}")
    
    def guardar_inventario(self):
        """Guardar el inventario actual."""
        try:
            os.makedirs(os.path.dirname(self.inventario_file), exist_ok=True)
            with open(self.inventario_file, 'w', encoding='utf-8') as f:
                json.dump(self.inventario, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Error guardando inventario: {e}")
    
    def verificar_disponibilidad(self, pastel_info: dict, extras: list) -> Tuple[bool, List[str]]:
        """
        Verificar si hay suficientes insumos para un pedido.
        
        Returns:
            Tuple[bool, List[str]]: (disponible, lista_de_faltantes)
        """
        try:
            # Determinar tamaño del pastel
            tamaño = self._determinar_tamaño(pastel_info)
            
            # Obtener receta base
            receta_base = self.recetas_fijas.get(tamaño, self.recetas_fijas["mediano"])
            
            # Agregar insumos específicos del tipo de pastel
            receta_completa = self._agregar_insumos_especificos(receta_base, pastel_info, extras)
            
            # Verificar disponibilidad
            faltantes = []
            for insumo, cantidad_necesaria in receta_completa.items():
                if insumo in self.inventario:
                    cantidad_actual = self.inventario[insumo]["cantidad_actual"]
                    cantidad_numerica = self._convertir_a_numero(cantidad_necesaria)
                    cantidad_actual_numerica = self._convertir_a_numero(str(cantidad_actual) + self.inventario[insumo]["unidad"])
                    
                    if cantidad_actual_numerica < cantidad_numerica:
                        diferencia = cantidad_numerica - cantidad_actual_numerica
                        faltantes.append(f"{insumo}: faltan {diferencia}{self._obtener_unidad_base(cantidad_necesaria)}")
            
            return len(faltantes) == 0, faltantes
            
        except Exception as e:
            print(f"❌ Error verificando disponibilidad: {e}")
            return False, [f"Error verificando inventario: {e}"]
    
    def descontar_insumos(self, pastel_info: dict, extras: list) -> bool:
        """Descontar insumos del inventario."""
        try:
            # Determinar tamaño del pastel
            tamaño = self._determinar_tamaño(pastel_info)
            
            # Obtener receta completa
            receta_base = self.recetas_fijas.get(tamaño, self.recetas_fijas["mediano"])
            receta_completa = self._agregar_insumos_especificos(receta_base, pastel_info, extras)
            
            # Descontar cada insumo
            for insumo, cantidad_necesaria in receta_completa.items():
                if insumo in self.inventario:
                    cantidad_numerica = self._convertir_a_numero(cantidad_necesaria)
                    cantidad_actual = self.inventario[insumo]["cantidad_actual"]
                    
                    # Convertir cantidad actual a número
                    if isinstance(cantidad_actual, str):
                        cantidad_actual = self._convertir_a_numero(cantidad_actual + self.inventario[insumo]["unidad"])
                    
                    nueva_cantidad = max(0, cantidad_actual - cantidad_numerica)
                    self.inventario[insumo]["cantidad_actual"] = nueva_cantidad
            
            self.guardar_inventario()
            return True
            
        except Exception as e:
            print(f"❌ Error descontando insumos: {e}")
            return False
    
    def _determinar_tamaño(self, pastel_info: dict) -> str:
        """Determinar el tamaño del pastel."""
        tipo = pastel_info.get("tipo", "mediano").lower()
        if "mini" in tipo:
            return "mini"
        elif "chico" in tipo or "pequeño" in tipo:
            return "chico"
        elif "grande" in tipo:
            return "grande"
        else:
            return "mediano"
    
    def _agregar_insumos_especificos(self, receta_base: dict, pastel_info: dict, extras: list) -> dict:
        """Agregar insumos específicos según el tipo de pastel y extras."""
        receta = receta_base.copy()
        
        # Insumos específicos por tipo de pastel
        nombre_pastel = pastel_info.get("nombre", "").lower()
        
        if "chocolate" in nombre_pastel:
            receta["Chocolate en Polvo"] = "100g"
        if "3 leches" in nombre_pastel:
            receta["Litro de Crema para Batir"] = "400ml"
            receta["Barra de Queso Crema"] = "200g"
        if "café" in nombre_pastel or "tiramisu" in nombre_pastel:
            receta["Frasco de Café"] = "80g"
        if "zanahoria" in nombre_pastel:
            receta["Zanahoria"] = "300g"
        
        # Agregar extras
        for extra in extras:
            nombre_extra = extra.get("nombre", "").lower()
            if "fresas" in nombre_extra:
                receta["Fresas"] = "200g"
            elif "chocolate" in nombre_extra:
                receta["Chocolate en Polvo"] = receta.get("Chocolate en Polvo", "0g").replace("g", "") + "50g"
            elif "nueces" in nombre_extra:
                receta["Nueces"] = "100g"
        
        return receta
    
    def _convertir_a_numero(self, cantidad_str: str) -> float:
        """Convertir string de cantidad a número."""
        try:
            # Extraer número de la string
            numero_str = ""
            for char in cantidad_str:
                if char.isdigit() or char == ".":
                    numero_str += char
            
            return float(numero_str) if numero_str else 0.0
        except:
            return 0.0
    
    def _obtener_unidad_base(self, cantidad_str: str) -> str:
        """Obtener la unidad de una cantidad."""
        unidades = ["kg", "g", "ml", "litros", "huevos", "unidades"]
        for unidad in unidades:
            if unidad in cantidad_str.lower():
                return unidad
        return ""
    
    def obtener_estado_inventario(self) -> dict:
        """Obtener el estado actual del inventario."""
        return self.inventario.copy()
