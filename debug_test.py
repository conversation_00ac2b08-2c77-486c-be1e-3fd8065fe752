#!/usr/bin/env python3
"""
Script de debug para identificar problemas específicos
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def debug_ai_service():
    """Debug específico del servicio de IA"""
    
    print("🔍 DEBUG: Iniciando debug del servicio de IA...")
    print("=" * 60)
    
    try:
        from backend.app.services.ai_service import AIService
        from backend.app.services.pasteleria_service import PasteleriaService
        from backend.app.utils.config import settings
        
        # Inicializar servicios
        PasteleriaService.initialize()
        AIService.initialize()
        
        print("✅ Servicios inicializados")
        print(f"🔑 API Key configurada: {bool(settings.OPENROUTER_API_KEY)}")
        print(f"🤖 Modelo configurado: {settings.AI_MODEL}")
        
        # Test 1: Pedido simple
        print("\n🧪 Test 1: Pedido simple")
        print("-" * 30)
        resultado1 = await AIService.get_response("pastel de chocolate", "Debug Test")
        
        if resultado1.get("error"):
            print(f"❌ Error: {resultado1['error']}")
        else:
            print(f"✅ Éxito: ${resultado1['pedido']['precio_total']}")
            print(f"📝 Mensaje: {resultado1['mensaje']}")
        
        # Test 2: Pedido con extras
        print("\n🧪 Test 2: Pedido con extras")
        print("-" * 30)
        resultado2 = await AIService.get_response("pastel de vainilla con fresas extra", "Debug Test")
        
        if resultado2.get("error"):
            print(f"❌ Error: {resultado2['error']}")
        else:
            print(f"✅ Éxito: ${resultado2['pedido']['precio_total']}")
            print(f"📝 Mensaje: {resultado2['mensaje']}")
        
        # Test 3: Pedido ambiguo
        print("\n🧪 Test 3: Pedido ambiguo")
        print("-" * 30)
        resultado3 = await AIService.get_response("quiero algo dulce", "Debug Test")
        
        if resultado3.get("error"):
            print(f"❌ Error: {resultado3['error']}")
        else:
            print(f"✅ Éxito: ${resultado3['pedido']['precio_total']}")
            print(f"📝 Mensaje: {resultado3['mensaje']}")
        
        # Test 4: Sistema de respaldo directo
        print("\n🧪 Test 4: Sistema de respaldo directo")
        print("-" * 30)
        resultado4 = AIService._process_with_fallback("pastel de chocolate con nueces", "Debug Test")
        
        if resultado4.get("error"):
            print(f"❌ Error: {resultado4['error']}")
        else:
            print(f"✅ Éxito: ${resultado4['pedido']['precio_total']}")
            print(f"📝 Mensaje: {resultado4['mensaje']}")
        
        print("\n" + "=" * 60)
        print("🎉 Debug completado!")
        
    except Exception as e:
        print(f"❌ Error crítico en debug: {e}")
        import traceback
        traceback.print_exc()

async def debug_pasteleria_service():
    """Debug del servicio de pastelería"""
    
    print("\n🔍 DEBUG: Servicio de Pastelería")
    print("=" * 40)
    
    try:
        from backend.app.services.pasteleria_service import PasteleriaService
        
        # Inicializar
        PasteleriaService.initialize()
        servicio = PasteleriaService.get_instance()
        
        # Verificar datos
        pasteles = servicio.obtener_todos_pasteles()
        extras = servicio.obtener_todos_extras()
        
        print(f"✅ Pasteles cargados: {len(pasteles)}")
        print(f"✅ Extras cargados: {len(extras)}")
        
        # Test de búsqueda
        print("\n🔍 Test de búsqueda de pasteles:")
        test_nombres = ["chocolate", "vainilla", "3 leches", "chocolate 3 leches"]
        
        for nombre in test_nombres:
            resultado = servicio.buscar_pastel(nombre)
            if resultado:
                print(f"  ✅ '{nombre}' -> {resultado['nombre']} (${resultado['precio']})")
            else:
                print(f"  ❌ '{nombre}' -> No encontrado")
        
        # Test de procesamiento
        print("\n🔍 Test de procesamiento básico:")
        test_pedidos = [
            "pastel de chocolate",
            "vainilla con fresas",
            "3 leches grande",
            "algo que no existe"
        ]
        
        for pedido in test_pedidos:
            resultado = servicio.procesar_pedido(pedido)
            if resultado["error"]:
                print(f"  ❌ '{pedido}' -> {resultado['error']}")
            else:
                print(f"  ✅ '{pedido}' -> ${resultado['precio_total']}")
        
    except Exception as e:
        print(f"❌ Error en debug de pastelería: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🐛 SCRIPT DE DEBUG - Pastelería Victoria's")
    print("=" * 60)
    
    # Debug del servicio de pastelería
    asyncio.run(debug_pasteleria_service())
    
    # Debug del servicio de IA
    asyncio.run(debug_ai_service())
