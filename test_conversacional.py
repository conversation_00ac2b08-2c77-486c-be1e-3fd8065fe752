#!/usr/bin/env python3
"""
Test del sistema conversacional mejorado
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_conversacion():
    """Test de la conversación paso a paso"""
    
    print("🗣️ TEST DEL SISTEMA CONVERSACIONAL")
    print("=" * 50)
    
    try:
        from backend.app.services.ai_service import AIService
        
        # Inicializar servicios
        AIService.initialize()
        print("✅ Servicios inicializados")
        
        # Simular una conversación completa
        conversacion = [
            "hola",
            "mi nombre es Juan Pérez",
            "quiero un pastel de chocolate 3 leches mediano",
            "sí, con fresas extra",
            "no, solo eso",
            "para el viernes 31 de mayo"
        ]
        
        print("\n🎭 Simulando conversación completa:")
        print("-" * 40)
        
        for i, mensaje in enumerate(conversacion, 1):
            print(f"\n{i}. Usuario: '{mensaje}'")
            
            resultado = await AIService.get_response(mensaje, "Cliente")
            
            if resultado.get("error"):
                print(f"   ❌ Error: {resultado['error']}")
            else:
                tipo = resultado.get('tipo', 'N/A')
                mensaje_ia = resultado.get('mensaje', 'N/A')
                
                print(f"   🤖 Tipo: {tipo}")
                print(f"   💬 IA: {mensaje_ia[:100]}...")
                
                # Si es pedido completo, mostrar detalles
                if tipo == 'pedido_completo':
                    print(f"   ✅ PEDIDO COMPLETO PROCESADO!")
                    print(f"   👤 Cliente: {resultado.get('nombre_cliente', 'N/A')}")
                    print(f"   🍰 Pastel: {resultado.get('pastel_detectado', 'N/A')}")
                    print(f"   💰 Total: ${resultado.get('precio_total', 'N/A')}")
                    print(f"   📅 Fecha: {resultado.get('fecha_entrega', 'N/A')}")
                    print(f"   📁 Guardado: {resultado.get('archivos_guardados', False)}")
                    break
        
        # Test de comandos especiales
        print("\n🔧 Test de comandos especiales:")
        print("-" * 30)
        
        # Test restablecer
        resultado = await AIService.get_response("restablecer", "Admin")
        print(f"Comando 'restablecer': {resultado.get('mensaje', 'N/A')}")
        
        # Test saludo simple
        resultado = await AIService.get_response("buenos días", "Nuevo Cliente")
        print(f"Saludo: {resultado.get('mensaje', 'N/A')[:80]}...")
        
        # Test consulta menú
        resultado = await AIService.get_response("qué pasteles tienen", "Cliente Curioso")
        print(f"Consulta menú: {resultado.get('tipo', 'N/A')} - {resultado.get('mensaje', 'N/A')[:60]}...")
        
        print("\n" + "=" * 50)
        print("🎉 TEST CONVERSACIONAL COMPLETADO!")
        print("\nFuncionalidades probadas:")
        print("✅ Saludo personalizado y solicitud de nombre")
        print("✅ Recopilación paso a paso de información")
        print("✅ Validación de información completa")
        print("✅ Procesamiento solo cuando todo está listo")
        print("✅ Guardado automático de notas")
        print("✅ Comandos especiales (restablecer)")
        print("✅ Diferentes tipos de respuesta")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test conversacional: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_conversacion())
    if not resultado:
        print("\n❌ TEST CONVERSACIONAL FALLÓ")
        sys.exit(1)
    else:
        print("\n✅ TEST CONVERSACIONAL EXITOSO")
