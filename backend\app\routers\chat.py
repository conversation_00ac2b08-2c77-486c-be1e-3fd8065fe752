"""
Router para el endpoint de chat de la pastelería.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from ..services.ai_service import AIService
from ..services.pasteleria_service import PasteleriaService

# Crear el router
router = APIRouter(
    prefix="/api",
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)

# Modelo de datos para la solicitud de pedido
class PedidoRequest(BaseModel):
    pedido: str
    nombre_cliente: str = "Cliente"

# Modelo de datos para la respuesta de pedido
class PedidoResponse(BaseModel):
    error: str | None = None
    mensaje: str | None = None
    nota_cliente: str | None = None
    nota_pastelera: str | None = None
    precio_total: float | None = None

@router.post("/pedido", response_model=PedidoResponse)
async def procesar_pedido(request: PedidoRequest):
    """
    Endpoint para procesar pedidos de pastelería.

    Args:
        request: La solicitud que contiene el pedido y el nombre del cliente.

    Returns:
        Una respuesta con la información del pedido procesado y las notas.

    Raises:
        HTTPException: Si ocurre un error al procesar la solicitud.
    """
    try:
        # Procesar el pedido usando el servicio de IA
        resultado = await AIService.get_response(request.pedido, request.nombre_cliente)

        if resultado.get("error"):
            return PedidoResponse(
                error=resultado["error"],
                mensaje=None,
                nota_cliente=None,
                nota_pastelera=None,
                precio_total=None
            )

        # Obtener precio total de manera segura
        precio_total = None
        if resultado.get("pedido") and isinstance(resultado["pedido"], dict):
            precio_total = resultado["pedido"].get("precio_total")
        elif "precio_total" in resultado:
            precio_total = resultado["precio_total"]

        return PedidoResponse(
            error=None,
            mensaje=resultado.get("mensaje", "Pedido procesado exitosamente"),
            nota_cliente=resultado.get("nota_cliente", ""),
            nota_pastelera=resultado.get("nota_pastelera", ""),
            precio_total=precio_total
        )

    except Exception as e:
        # Capturar cualquier excepción y devolver un error
        raise HTTPException(status_code=500, detail=f"Error al procesar el pedido: {str(e)}")


@router.get("/menu")
async def obtener_menu():
    """
    Endpoint para obtener el menú completo de la pastelería.

    Returns:
        Diccionario con todos los pasteles, extras e insumos disponibles.
    """
    try:
        # Inicializar el servicio si no se ha hecho
        PasteleriaService.initialize()
        servicio = PasteleriaService.get_instance()

        return {
            "pasteles": servicio.obtener_todos_pasteles(),
            "extras": servicio.obtener_todos_extras(),
            "insumos": servicio.obtener_todos_insumos()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el menú: {str(e)}")


@router.get("/menu-descripciones")
async def obtener_menu_con_descripciones():
    """
    Endpoint para obtener el menú con descripciones atractivas para el cliente.

    Returns:
        Diccionario con el menú organizado y con descripciones.
    """
    try:
        # Inicializar el servicio si no se ha hecho
        PasteleriaService.initialize()
        servicio = PasteleriaService.get_instance()

        return servicio.obtener_menu_con_descripciones()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el menú: {str(e)}")
