#!/usr/bin/env python3
"""
Script para probar la API de Pastelería Victoria's
"""

import requests
import json

def test_api():
    """Probar la API de pedidos."""
    
    print("🧪 Probando API de Pastelería Victoria's...")
    print("=" * 50)
    
    # URL base de la API
    base_url = "http://localhost:8000/api"
    
    # Test 1: Probar endpoint de menú
    try:
        print("📋 Probando endpoint de menú...")
        response = requests.get(f"{base_url}/menu-descripciones", timeout=10)
        if response.status_code == 200:
            print("✅ Endpoint de menú funciona correctamente")
        else:
            print(f"❌ Error en endpoint de menú: {response.status_code}")
    except Exception as e:
        print(f"❌ Error conectando al endpoint de menú: {e}")
    
    # Test 2: Probar endpoint de pedidos
    try:
        print("\n🍰 Probando endpoint de pedidos...")
        
        # Datos de prueba
        pedido_data = {
            "pedido": "Quiero un pastel de chocolate con fresas extra",
            "nombre_cliente": "Cliente Test"
        }
        
        response = requests.post(
            f"{base_url}/pedido",
            json=pedido_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("error"):
                print(f"⚠️ API respondió con error: {data['error']}")
            else:
                print("✅ Pedido procesado exitosamente!")
                print(f"💰 Precio total: ${data.get('precio_total', 'N/A')}")
                print(f"📝 Mensaje: {data.get('mensaje', 'N/A')}")
                
                if data.get('nota_cliente'):
                    print("\n📋 NOTA PARA EL CLIENTE:")
                    print("-" * 30)
                    print(data['nota_cliente'][:200] + "..." if len(data['nota_cliente']) > 200 else data['nota_cliente'])
                
                if data.get('nota_pastelera'):
                    print("\n👩‍🍳 NOTA PARA LA PASTELERA:")
                    print("-" * 30)
                    print(data['nota_pastelera'][:200] + "..." if len(data['nota_pastelera']) > 200 else data['nota_pastelera'])
        else:
            print(f"❌ Error en endpoint de pedidos: {response.status_code}")
            print(f"Respuesta: {response.text}")
            
    except Exception as e:
        print(f"❌ Error conectando al endpoint de pedidos: {e}")
    
    # Test 3: Probar varios tipos de pedidos
    print("\n🔄 Probando diferentes tipos de pedidos...")
    
    pedidos_test = [
        "Pastel de vainilla con nueces",
        "3 leches con fresas y chocolate extra",
        "Zanahoria chico",
        "Quiero algo de chocolate"
    ]
    
    for i, pedido in enumerate(pedidos_test, 1):
        try:
            print(f"\n{i}. Probando: '{pedido}'")
            response = requests.post(
                f"{base_url}/pedido",
                json={"pedido": pedido, "nombre_cliente": f"Test{i}"},
                headers={"Content-Type": "application/json"},
                timeout=20
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("error"):
                    print(f"   ⚠️ Error: {data['error']}")
                else:
                    print(f"   ✅ Éxito: ${data.get('precio_total', 'N/A')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Pruebas de API completadas!")

if __name__ == "__main__":
    test_api()
