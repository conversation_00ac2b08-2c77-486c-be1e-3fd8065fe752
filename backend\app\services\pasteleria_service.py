"""
Servicio de base de datos para la pastelería.
"""

from typing import List, Dict, Any, Optional, Tuple
import re
from datetime import datetime

class PasteleriaService:
    """Servicio para el manejo de datos de la pastelería."""

    _instance = None
    _initialized = False

    def __init__(self):
        self.pasteles = {}
        self.insumos = {}
        self.extras = {}
        self._load_data()

    @classmethod
    def get_instance(cls):
        """Obtener la instancia singleton del servicio."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @classmethod
    def initialize(cls):
        """Inicializar el servicio de pastelería."""
        if not cls._initialized:
            instance = cls.get_instance()
            cls._initialized = True

    def _load_data(self):
        """Cargar los datos de pasteles e insumos."""
        # Datos de pasteles y postres
        self.pasteles = {
            # Pasteles grandes (15 personas)
            "3 leches grande": {"rebanadas": 15, "precio": 680, "tipo": "grande"},
            "vainilla grande": {"rebanadas": 15, "precio": 680, "tipo": "grande"},
            "chocolate 3 leches grande": {"rebanadas": 15, "precio": 670, "tipo": "grande"},
            "zanahoria grande": {"rebanadas": 15, "precio": 680, "tipo": "grande"},
            "snickers grande": {"rebanadas": 15, "precio": 690, "tipo": "grande"},
            "chocolate aleman grande": {"rebanadas": 15, "precio": 670, "tipo": "grande"},
            "oreo grande": {"rebanadas": 15, "precio": 670, "tipo": "grande"},
            "reeses grande": {"rebanadas": 15, "precio": 690, "tipo": "grande"},

            # Pasteles medianos (12 rebanadas) - ANTES ERAN "GRANDES"
            "3 leches": {"rebanadas": 12, "precio": 540, "tipo": "mediano"},
            "vainilla": {"rebanadas": 12, "precio": 540, "tipo": "mediano"},
            "chocolate 3 leches": {"rebanadas": 12, "precio": 530, "tipo": "mediano"},
            "zanahoria": {"rebanadas": 12, "precio": 540, "tipo": "mediano"},
            "snickers": {"rebanadas": 12, "precio": 550, "tipo": "mediano"},
            "chocolate aleman": {"rebanadas": 12, "precio": 530, "tipo": "mediano"},
            "oreo": {"rebanadas": 12, "precio": 530, "tipo": "mediano"},
            "reeses": {"rebanadas": 12, "precio": 550, "tipo": "mediano"},
            "3 leches mediano": {"rebanadas": 12, "precio": 540, "tipo": "mediano"},
            "vainilla mediano": {"rebanadas": 12, "precio": 540, "tipo": "mediano"},
            "chocolate 3 leches mediano": {"rebanadas": 12, "precio": 530, "tipo": "mediano"},
            "zanahoria mediano": {"rebanadas": 12, "precio": 540, "tipo": "mediano"},
            "snickers mediano": {"rebanadas": 12, "precio": 550, "tipo": "mediano"},
            "chocolate aleman mediano": {"rebanadas": 12, "precio": 530, "tipo": "mediano"},
            "oreo mediano": {"rebanadas": 12, "precio": 530, "tipo": "mediano"},
            "reeses mediano": {"rebanadas": 12, "precio": 550, "tipo": "mediano"},

            # Pasteles chicos (6 rebanadas)
            "3 leches chico": {"rebanadas": 6, "precio": 320, "tipo": "chico"},
            "vainilla chico": {"rebanadas": 6, "precio": 320, "tipo": "chico"},
            "chocolate 3 leches chico": {"rebanadas": 6, "precio": 310, "tipo": "chico"},
            "zanahoria chico": {"rebanadas": 6, "precio": 320, "tipo": "chico"},
            "snickers chico": {"rebanadas": 6, "precio": 330, "tipo": "chico"},
            "chocolate aleman chico": {"rebanadas": 6, "precio": 310, "tipo": "chico"},
            "oreo chico": {"rebanadas": 6, "precio": 310, "tipo": "chico"},
            "reeses chico": {"rebanadas": 6, "precio": 330, "tipo": "chico"},

            # Pasteles mini (1 rebanada)
            "3 leches mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "vainilla mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "chocolate 3 leches mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "zanahoria mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "snickers mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "chocolate aleman mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "oreo mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},
            "reeses mini": {"rebanadas": 1, "precio": 80, "tipo": "mini"},

            # Otros postres
            "cakepop": {"rebanadas": 1, "precio": 18, "tipo": "postre"},
            "carlota": {"rebanadas": 1, "precio": 45, "tipo": "postre"},
            "tiramisu": {"rebanadas": 1, "precio": 45, "tipo": "postre"},
            "galleta chispas": {"rebanadas": 1, "precio": 45, "tipo": "postre"},
            "rebanada de chocoflan": {"rebanadas": 1, "precio": 85, "tipo": "rebanada"},
            "rebanada de panque": {"rebanadas": 1, "precio": 25, "tipo": "rebanada"},
        }

        # Datos de extras (todos a $40)
        self.extras = {
            "fresas": {"precio": 40, "tipo": "fruta"},
            "frambuesas": {"precio": 40, "tipo": "fruta"},
            "moras": {"precio": 40, "tipo": "fruta"},
            "durazno": {"precio": 40, "tipo": "fruta"},
            "platano": {"precio": 40, "tipo": "fruta"},
            "kiwi": {"precio": 40, "tipo": "fruta"},
            "chocolate": {"precio": 40, "tipo": "complemento"},
            "chocolate extra": {"precio": 40, "tipo": "complemento"},
            "crema": {"precio": 40, "tipo": "complemento"},
            "crema extra": {"precio": 40, "tipo": "complemento"},
            "nueces": {"precio": 40, "tipo": "complemento"},
            "almendras": {"precio": 40, "tipo": "complemento"},
            "caramelo": {"precio": 40, "tipo": "complemento"},
            "dulce de leche": {"precio": 40, "tipo": "complemento"},
            "mermelada": {"precio": 40, "tipo": "complemento"},
            "chispas de chocolate": {"precio": 40, "tipo": "complemento"},
        }

        # Datos de insumos
        self.insumos = {
            "harina de trigo": {"precio": 100, "cantidad": "5 kilogramos"},
            "cartera de huevo": {"precio": 110, "cantidad": "30 huevos"},
            "azucar regular": {"precio": 25, "cantidad": "1 kilogramo"},
            "litro aceite": {"precio": 45, "cantidad": "1 litro"},
            "bote de margarina": {"precio": 200, "cantidad": "1 bote"},
            "mantequilla": {"precio": 100, "cantidad": "460 gramos"},
            "spray antiadherente": {"precio": 100, "cantidad": "1 bote"},
            "litro de vainilla": {"precio": 100, "cantidad": "1 litro"},
            "frasco de cafe": {"precio": 125, "cantidad": "200 gramos"},
            "bolsita de canela": {"precio": 30, "cantidad": "1 bolsa"},
            "agua mineral": {"precio": 30, "cantidad": "2 litros"},
            "litro de crema para batir": {"precio": 120, "cantidad": "1 litro"},
            "barra de queso crema": {"precio": 200, "cantidad": "1 barra"},
            "bolsa de azucar glass": {"precio": 30, "cantidad": "1 bolsa"},
            "lata de frutas enlatadas": {"precio": 60, "cantidad": "1 lata"},
            "cajita de fresas": {"precio": 70, "cantidad": "1 caja"},
            "kilo de limon": {"precio": 25, "cantidad": "1 kilogramo"},
            "kilo de platanos": {"precio": 37, "cantidad": "1 kilogramo"},
            "kilo de naranjas": {"precio": 38, "cantidad": "1 kilogramo"},
        }

    def buscar_pastel(self, nombre: str) -> Optional[Dict[str, Any]]:
        """Buscar un pastel por nombre."""
        nombre_lower = nombre.lower().strip()

        # Buscar coincidencia exacta
        if nombre_lower in self.pasteles:
            return {"nombre": nombre_lower, **self.pasteles[nombre_lower]}

        # Buscar coincidencia parcial
        for pastel_nombre, datos in self.pasteles.items():
            if nombre_lower in pastel_nombre or pastel_nombre in nombre_lower:
                return {"nombre": pastel_nombre, **datos}

        return None

    def buscar_extra(self, nombre: str) -> Optional[Dict[str, Any]]:
        """Buscar un extra por nombre."""
        nombre_lower = nombre.lower().strip()

        # Buscar coincidencia exacta
        if nombre_lower in self.extras:
            return {"nombre": nombre_lower, **self.extras[nombre_lower]}

        # Buscar coincidencia parcial
        for extra_nombre, datos in self.extras.items():
            if nombre_lower in extra_nombre or extra_nombre in nombre_lower:
                return {"nombre": extra_nombre, **datos}

        return None

    def buscar_insumo(self, nombre: str) -> Optional[Dict[str, Any]]:
        """Buscar un insumo por nombre."""
        nombre_lower = nombre.lower().strip()

        # Buscar coincidencia exacta
        if nombre_lower in self.insumos:
            return {"nombre": nombre_lower, **self.insumos[nombre_lower]}

        # Buscar coincidencia parcial
        for insumo_nombre, datos in self.insumos.items():
            if nombre_lower in insumo_nombre or insumo_nombre in nombre_lower:
                return {"nombre": insumo_nombre, **datos}

        return None

    def procesar_pedido(self, texto_pedido: str) -> Dict[str, Any]:
        """
        Procesar un pedido de texto libre y extraer información.

        Args:
            texto_pedido: Texto del pedido del usuario

        Returns:
            Diccionario con información del pedido procesado
        """
        resultado = {
            "pastel": None,
            "extras": [],
            "precio_total": 0,
            "insumos_necesarios": [],
            "error": None
        }

        texto_lower = texto_pedido.lower()

        # Buscar pastel principal
        pastel_encontrado = None
        for nombre_pastel in self.pasteles.keys():
            if nombre_pastel in texto_lower:
                pastel_encontrado = self.buscar_pastel(nombre_pastel)
                break

        if not pastel_encontrado:
            resultado["error"] = "No se pudo identificar el tipo de pastel en el pedido."
            return resultado

        resultado["pastel"] = pastel_encontrado
        resultado["precio_total"] += pastel_encontrado["precio"]

        # Buscar extras
        for nombre_extra in self.extras.keys():
            if nombre_extra in texto_lower:
                extra = self.buscar_extra(nombre_extra)
                if extra:
                    resultado["extras"].append(extra)
                    resultado["precio_total"] += extra["precio"]

        # Calcular insumos necesarios (simplificado)
        resultado["insumos_necesarios"] = self._calcular_insumos(pastel_encontrado, resultado["extras"])

        return resultado

    def _calcular_insumos(self, pastel: Dict[str, Any], extras: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calcular insumos necesarios para un pedido con costos detallados."""
        insumos_detallados = []

        # Insumos base para cualquier pastel
        insumos_base = [
            {
                "nombre": "Harina de Trigo",
                "cantidad_total": "5 kilogramos",
                "precio_total": 100,
                "cantidad_usada": "800g",
                "porcentaje_usado": 16,  # 800g de 5kg
                "costo_usado": 16
            },
            {
                "nombre": "Cartera de Huevo",
                "cantidad_total": "30 huevos",
                "precio_total": 110,
                "cantidad_usada": "4 huevos",
                "porcentaje_usado": 13.33,  # 4 de 30
                "costo_usado": 14.67
            },
            {
                "nombre": "Azúcar Regular",
                "cantidad_total": "1 kilogramo",
                "precio_total": 25,
                "cantidad_usada": "300g",
                "porcentaje_usado": 30,  # 300g de 1kg
                "costo_usado": 7.5
            },
            {
                "nombre": "Mantequilla",
                "cantidad_total": "460 gramos",
                "precio_total": 100,
                "cantidad_usada": "150g",
                "porcentaje_usado": 32.61,  # 150g de 460g
                "costo_usado": 32.61
            },
            {
                "nombre": "Litro de Vainilla",
                "cantidad_total": "1 litro",
                "precio_total": 100,
                "cantidad_usada": "30ml",
                "porcentaje_usado": 3,  # 30ml de 1000ml
                "costo_usado": 3
            }
        ]

        insumos_detallados.extend(insumos_base)

        # Insumos específicos según el tipo de pastel
        if "chocolate" in pastel["nombre"].lower():
            insumos_detallados.append({
                "nombre": "Frasco de Café",
                "cantidad_total": "200 gramos",
                "precio_total": 125,
                "cantidad_usada": "80g",
                "porcentaje_usado": 40,  # 80g de 200g
                "costo_usado": 50
            })

        if "3 leches" in pastel["nombre"].lower():
            insumos_detallados.append({
                "nombre": "Litro de Crema para Batir",
                "cantidad_total": "1 litro",
                "precio_total": 120,
                "cantidad_usada": "400ml",
                "porcentaje_usado": 40,  # 400ml de 1000ml
                "costo_usado": 48
            })
            insumos_detallados.append({
                "nombre": "Barra de Queso Crema",
                "cantidad_total": "1 barra",
                "precio_total": 200,
                "cantidad_usada": "200g",
                "porcentaje_usado": 100,  # toda la barra
                "costo_usado": 200
            })

        if "zanahoria" in pastel["nombre"].lower():
            insumos_detallados.append({
                "nombre": "Bolsita de Canela",
                "cantidad_total": "1 bolsa",
                "precio_total": 30,
                "cantidad_usada": "5g",
                "porcentaje_usado": 25,  # estimado
                "costo_usado": 7.5
            })

        # Insumos para extras
        for extra in extras:
            if "fresas" in extra["nombre"].lower():
                insumos_detallados.append({
                    "nombre": "Cajita de Fresas",
                    "cantidad_total": "1 caja",
                    "precio_total": 70,
                    "cantidad_usada": "200g",
                    "porcentaje_usado": 100,
                    "costo_usado": 70
                })
            elif "chocolate" in extra["nombre"].lower():
                insumos_detallados.append({
                    "nombre": "Chocolate Extra",
                    "cantidad_total": "500g",
                    "precio_total": 80,
                    "cantidad_usada": "100g",
                    "porcentaje_usado": 20,
                    "costo_usado": 16
                })
            elif "nueces" in extra["nombre"].lower():
                insumos_detallados.append({
                    "nombre": "Nueces",
                    "cantidad_total": "250g",
                    "precio_total": 60,
                    "cantidad_usada": "80g",
                    "porcentaje_usado": 32,
                    "costo_usado": 19.2
                })
            elif "caramelo" in extra["nombre"].lower():
                insumos_detallados.append({
                    "nombre": "Caramelo",
                    "cantidad_total": "300ml",
                    "precio_total": 45,
                    "cantidad_usada": "100ml",
                    "porcentaje_usado": 33.33,
                    "costo_usado": 15
                })

        return insumos_detallados

    def generar_nota_cliente(self, pedido: Dict[str, Any], nombre_cliente: str) -> str:
        """Generar nota para el cliente."""
        fecha_actual = datetime.now().strftime("%d/%m/%Y %H:%M")
        fecha_entrega = datetime.now().strftime("%d/%m/%Y")  # Simplificado, mismo día

        nota = f"""
=== NOTA PARA EL CLIENTE ===
Fecha del pedido: {fecha_actual}
Cliente: {nombre_cliente}

PEDIDO:
- {pedido['pastel']['nombre'].title()}: ${pedido['pastel']['precio']}
"""

        for extra in pedido['extras']:
            nota += f"- Extra {extra['nombre']}: ${extra['precio']}\n"

        nota += f"""
TOTAL: ${pedido['precio_total']}
Fecha de entrega: {fecha_entrega}

¡Gracias por su preferencia!
        """

        return nota.strip()

    def generar_nota_pastelera(self, pedido: Dict[str, Any], nombre_cliente: str) -> str:
        """Generar nota para la pastelera con costos detallados."""
        fecha_actual = datetime.now().strftime("%d/%m/%Y %H:%M")

        nota = f"""
=== NOTA PARA LA PASTELERA ===
Fecha: {fecha_actual}
Cliente: {nombre_cliente}

ESPECIFICACIONES DEL PEDIDO:
- Pastel: {pedido['pastel']['nombre'].title()}
- Tipo: {pedido['pastel']['tipo']}
- Rebanadas: {pedido['pastel']['rebanadas']}
- Precio del pastel: ${pedido['pastel']['precio']}
"""

        if pedido['extras']:
            nota += "\nEXTRAS SOLICITADOS:\n"
            for extra in pedido['extras']:
                nota += f"- {extra['nombre'].title()}: ${extra['precio']}\n"

        nota += f"\nPRECIO TOTAL AL CLIENTE: ${pedido['precio_total']}\n"

        nota += "\n" + "="*50
        nota += "\nCÁLCULO DE COSTOS DE INSUMOS:"
        nota += "\n" + "="*50

        costo_total_insumos = 0
        for insumo in pedido['insumos_necesarios']:
            nota += f"""
• {insumo['nombre']}:
  - Cantidad total disponible: {insumo['cantidad_total']}
  - Precio total del insumo: ${insumo['precio_total']}
  - Cantidad a usar: {insumo['cantidad_usada']}
  - Porcentaje usado: {insumo['porcentaje_usado']:.1f}%
  - Costo de lo usado: ${insumo['costo_usado']:.2f}
"""
            costo_total_insumos += insumo['costo_usado']

        nota += f"\n" + "="*50
        nota += f"\nRESUMEN FINANCIERO:"
        nota += f"\n" + "="*50
        nota += f"\nCosto total de insumos: ${costo_total_insumos:.2f}"
        nota += f"\nPrecio de venta: ${pedido['precio_total']}"
        ganancia = pedido['precio_total'] - costo_total_insumos
        margen = (ganancia / pedido['precio_total']) * 100 if pedido['precio_total'] > 0 else 0
        nota += f"\nGanancia bruta: ${ganancia:.2f}"
        nota += f"\nMargen de ganancia: {margen:.1f}%"

        return nota.strip()

    def obtener_todos_pasteles(self) -> Dict[str, Dict[str, Any]]:
        """Obtener todos los pasteles disponibles."""
        return self.pasteles.copy()

    def obtener_todos_extras(self) -> Dict[str, Dict[str, Any]]:
        """Obtener todos los extras disponibles."""
        return self.extras.copy()

    def obtener_todos_insumos(self) -> Dict[str, Dict[str, Any]]:
        """Obtener todos los insumos disponibles."""
        return self.insumos.copy()

    def obtener_menu_con_descripciones(self) -> Dict[str, Any]:
        """Obtener menú con descripciones atractivas para mostrar al cliente."""
        return {
            "pasteles_grandes": {
                "titulo": "Pasteles Grandes (15 personas) - Perfectos para grandes celebraciones",
                "descripcion": "Nuestros pasteles más grandes, ideales para fiestas y eventos especiales:",
                "items": [
                    {"nombre": "3 Leches Grande", "descripcion": "Clásico pastel empapado en tres tipos de leche, suave y cremoso", "rebanadas": 15},
                    {"nombre": "Vainilla Grande", "descripcion": "Tradicional pastel de vainilla, esponjoso y aromático", "rebanadas": 15},
                    {"nombre": "Chocolate 3 Leches Grande", "descripcion": "La perfecta combinación de chocolate y tres leches", "rebanadas": 15},
                    {"nombre": "Zanahoria Grande", "descripcion": "Pastel húmedo de zanahoria con especias y nueces", "rebanadas": 15},
                    {"nombre": "Snickers Grande", "descripcion": "Inspirado en el chocolate favorito, con caramelo y cacahuates", "rebanadas": 15},
                    {"nombre": "Chocolate Alemán Grande", "descripcion": "Rico pastel de chocolate con coco y nueces", "rebanadas": 15},
                    {"nombre": "Oreo Grande", "descripcion": "Delicioso pastel con galletas Oreo trituradas", "rebanadas": 15},
                    {"nombre": "Reeses Grande", "descripcion": "Irresistible combinación de chocolate y mantequilla de maní", "rebanadas": 15}
                ]
            },
            "pasteles_medianos": {
                "titulo": "Pasteles Medianos (12 rebanadas) - Perfectos para compartir",
                "descripcion": "Tenemos unos pasteles muy buenos, esponjosos y deliciosos:",
                "items": [
                    {"nombre": "3 Leches", "descripcion": "Clásico pastel empapado en tres tipos de leche, suave y cremoso", "rebanadas": 12},
                    {"nombre": "Vainilla", "descripcion": "Tradicional pastel de vainilla, esponjoso y aromático", "rebanadas": 12},
                    {"nombre": "Chocolate 3 Leches", "descripcion": "La perfecta combinación de chocolate y tres leches", "rebanadas": 12},
                    {"nombre": "Zanahoria", "descripcion": "Pastel húmedo de zanahoria con especias y nueces", "rebanadas": 12},
                    {"nombre": "Snickers", "descripcion": "Inspirado en el chocolate favorito, con caramelo y cacahuates", "rebanadas": 12},
                    {"nombre": "Chocolate Alemán", "descripcion": "Rico pastel de chocolate con coco y nueces", "rebanadas": 12},
                    {"nombre": "Oreo", "descripcion": "Delicioso pastel con galletas Oreo trituradas", "rebanadas": 12},
                    {"nombre": "Reeses", "descripcion": "Irresistible combinación de chocolate y mantequilla de maní", "rebanadas": 12}
                ]
            },
            "pasteles_chicos": {
                "titulo": "Pasteles Chicos (6 rebanadas) - Perfectos para ocasiones íntimas",
                "descripcion": "Nuestros pasteles en tamaño chico para reuniones pequeñas:",
                "items": [
                    {"nombre": "3 Leches Chico", "descripcion": "Clásico 3 leches en tamaño chico", "rebanadas": 6},
                    {"nombre": "Vainilla Chico", "descripcion": "Tradicional vainilla en tamaño chico", "rebanadas": 6},
                    {"nombre": "Chocolate 3 Leches Chico", "descripcion": "Chocolate y tres leches en tamaño chico", "rebanadas": 6},
                    {"nombre": "Zanahoria Chico", "descripcion": "Zanahoria con especias en tamaño chico", "rebanadas": 6},
                    {"nombre": "Snickers Chico", "descripcion": "Sabor Snickers en tamaño chico", "rebanadas": 6},
                    {"nombre": "Chocolate Alemán Chico", "descripcion": "Chocolate alemán en tamaño chico", "rebanadas": 6},
                    {"nombre": "Oreo Chico", "descripcion": "Oreo delicioso en tamaño chico", "rebanadas": 6},
                    {"nombre": "Reeses Chico", "descripcion": "Reeses irresistible en tamaño chico", "rebanadas": 6}
                ]
            },
            "pasteles_mini": {
                "titulo": "Pasteles Mini (1 rebanada) - Perfectos para una persona",
                "descripcion": "Todos nuestros sabores en porciones individuales:",
                "items": [
                    {"nombre": "3 Leches Mini", "descripcion": "Porción individual de nuestro clásico 3 leches", "rebanadas": 1},
                    {"nombre": "Vainilla Mini", "descripcion": "Porción individual de vainilla", "rebanadas": 1},
                    {"nombre": "Chocolate 3 Leches Mini", "descripcion": "Porción individual de chocolate 3 leches", "rebanadas": 1},
                    {"nombre": "Zanahoria Mini", "descripcion": "Porción individual de zanahoria", "rebanadas": 1},
                    {"nombre": "Snickers Mini", "descripcion": "Porción individual de Snickers", "rebanadas": 1},
                    {"nombre": "Chocolate Alemán Mini", "descripcion": "Porción individual de chocolate alemán", "rebanadas": 1},
                    {"nombre": "Oreo Mini", "descripcion": "Porción individual de Oreo", "rebanadas": 1},
                    {"nombre": "Reeses Mini", "descripcion": "Porción individual de Reeses", "rebanadas": 1}
                ]
            },
            "otros_postres": {
                "titulo": "Otros Postres Deliciosos",
                "descripcion": "Variedad de postres especiales para todos los gustos:",
                "items": [
                    {"nombre": "Cakepop", "descripcion": "Pequeñas bolitas de pastel cubiertas de chocolate", "rebanadas": 1},
                    {"nombre": "Carlota", "descripcion": "Postre frío con galletas y crema", "rebanadas": 1},
                    {"nombre": "Tiramisu", "descripcion": "Clásico postre italiano con café y mascarpone", "rebanadas": 1},
                    {"nombre": "Galleta Chispas", "descripcion": "Galletas caseras con chispas de chocolate", "rebanadas": 1},
                    {"nombre": "Rebanada de Chocoflan", "descripcion": "Combinación perfecta de flan y chocolate", "rebanadas": 1},
                    {"nombre": "Rebanada de Panqué", "descripcion": "Panqué casero (zanahoria, plátano, marmoleado o chocolate)", "rebanadas": 1}
                ]
            },
            "extras": {
                "titulo": "Extras Deliciosos ($40 cada uno)",
                "descripcion": "Personaliza tu pastel con nuestros extras:",
                "items": [
                    {"nombre": "Fresas", "descripcion": "Fresas frescas y jugosas"},
                    {"nombre": "Frambuesas", "descripcion": "Frambuesas dulces y aromáticas"},
                    {"nombre": "Chocolate", "descripcion": "Chocolate extra para los amantes del cacao"},
                    {"nombre": "Nueces", "descripcion": "Nueces crujientes y nutritivas"},
                    {"nombre": "Caramelo", "descripcion": "Delicioso caramelo casero"},
                    {"nombre": "Crema", "descripcion": "Crema extra suave y cremosa"},
                    {"nombre": "Dulce de Leche", "descripcion": "Dulce de leche argentino"},
                    {"nombre": "Almendras", "descripcion": "Almendras tostadas y crujientes"}
                ]
            }
        }
