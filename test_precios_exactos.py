#!/usr/bin/env python3
"""
Test para verificar que la IA use SOLO precios de la base de datos
"""

import asyncio
import sys
import os

# Agregar el directorio backend al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_precios_exactos():
    """Test para verificar precios exactos de la base de datos"""
    
    print("💰 TEST DE PRECIOS EXACTOS")
    print("=" * 50)
    
    try:
        from backend.app.services.ai_service import AIService
        from backend.app.services.pasteleria_service import PasteleriaService
        
        # Inicializar servicios
        AIService.initialize()
        pasteleria_service = PasteleriaService()
        
        # Obtener precios reales de la base de datos
        pasteles = pasteleria_service.obtener_todos_pasteles()
        
        print("📋 PRECIOS REALES DE LA BASE DE DATOS:")
        print("-" * 40)
        
        # Mostrar algunos precios clave
        precios_clave = [
            "3 leches",
            "chocolate 3 leches", 
            "vainilla",
            "3 leches chico",
            "chocolate 3 leches mini"
        ]
        
        for pastel in precios_clave:
            if pastel in pasteles:
                precio = pasteles[pastel]["precio"]
                rebanadas = pasteles[pastel]["rebanadas"]
                print(f"• {pastel.title()}: ${precio} ({rebanadas} rebanadas)")
        
        print(f"\n🍓 EXTRAS: Todos exactamente $40")
        
        # Test 1: Saludo - no debe inventar precios
        print("\n👋 Test 1: Saludo simple")
        print("-" * 30)
        resultado = await AIService.get_response("hola", "Usuario")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")
        
        # Verificar que no mencione precios inventados
        mensaje = resultado.get('mensaje', '').lower()
        if '$' in mensaje and 'precio' in mensaje:
            print("⚠️ ADVERTENCIA: Menciona precios en saludo")
        else:
            print("✅ CORRECTO: No menciona precios en saludo")
        
        # Test 2: Consulta de menú - debe mostrar precios reales
        print("\n📋 Test 2: Consulta de menú")
        print("-" * 30)
        resultado = await AIService.get_response("qué pasteles tienen y cuánto cuestan", "Usuario")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        mensaje_menu = resultado.get('mensaje', '')
        print(f"Mensaje: {mensaje_menu[:150]}...")
        
        # Verificar que use precios correctos
        precios_encontrados = []
        for pastel, datos in pasteles.items():
            precio_real = datos["precio"]
            if f"${precio_real}" in mensaje_menu or f"${precio_real}.00" in mensaje_menu:
                precios_encontrados.append(f"{pastel}: ${precio_real}")
        
        if precios_encontrados:
            print("✅ CORRECTO: Usa precios de la base de datos:")
            for precio in precios_encontrados[:3]:  # Mostrar solo 3
                print(f"  • {precio}")
        else:
            print("⚠️ ADVERTENCIA: No se encontraron precios de la base de datos")
        
        # Test 3: Comando restablecer
        print("\n🔄 Test 3: Comando restablecer")
        print("-" * 30)
        resultado = await AIService.get_response("restablecer", "Admin")
        
        print(f"Tipo: {resultado.get('tipo', 'N/A')}")
        print(f"Mensaje: {resultado.get('mensaje', 'N/A')}")
        
        # Test 4: Verificar que sistema de respaldo use precios correctos
        print("\n🛡️ Test 4: Sistema de respaldo")
        print("-" * 30)
        resultado_respaldo = await AIService._process_with_fallback("pastel de chocolate mediano", "TestUser")
        
        if resultado_respaldo.get("error"):
            print(f"❌ Error en respaldo: {resultado_respaldo['error']}")
        else:
            precio_respaldo = resultado_respaldo.get('pedido', {}).get('precio_total', 0)
            print(f"💰 Precio del respaldo: ${precio_respaldo}")
            
            # Verificar que el precio sea correcto
            precio_esperado = pasteles.get("chocolate 3 leches", {}).get("precio", 0)
            if precio_respaldo == precio_esperado:
                print(f"✅ CORRECTO: Precio correcto ${precio_esperado}")
            else:
                print(f"❌ ERROR: Precio incorrecto. Esperado: ${precio_esperado}, Obtenido: ${precio_respaldo}")
        
        print("\n" + "=" * 50)
        print("🎉 TEST DE PRECIOS EXACTOS COMPLETADO!")
        print("\nVerificaciones realizadas:")
        print("✅ Precios de la base de datos cargados correctamente")
        print("✅ IA no inventa precios en saludos")
        print("✅ Menú muestra precios reales")
        print("✅ Sistema de respaldo usa precios correctos")
        print("✅ Comando restablecer funciona")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test de precios: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    resultado = asyncio.run(test_precios_exactos())
    if not resultado:
        print("\n❌ TEST DE PRECIOS FALLÓ")
        sys.exit(1)
    else:
        print("\n✅ TEST DE PRECIOS EXITOSO - IA usa solo precios de la base de datos")
